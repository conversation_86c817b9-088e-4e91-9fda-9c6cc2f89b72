import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { db } from "../models";
import { MessageType } from "../models/TicketMessage";
import { TicketStatus } from "../models/Ticket";
import { item_category } from "../models/Item";
import uploadService from "../helper/upload.service";
import { getUser, getPagination, getPaginatedItems } from "../utils/common";
import * as notificationService from "../services/notification.service";

// Get models from db object to ensure associations are set up (following recipe pattern)
const Ticket = db.Ticket;
const TicketMessage = db.TicketMessage;
const TicketHistory = db.TicketHistory;
const Item = db.Item;

/**
 * @description Add message to ticket with optional file attachment
 * @route POST /api/v1/private/tickets/:ticket_id/messages
 * @access Private (requires authentication)
 */
const addMessage = async (req: Request, res: Response): Promise<any> => {
  try {
    const { ticket_id } = req.params;
    const {
      message_text,
      message_type = MessageType.USER,
      is_private = false,
    } = req.body;
    const created_by = (req as any).user?.id || 0;

    // Verify ticket exists
    const ticket = await Ticket.findByPk(ticket_id);
    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND"),
      });
    }

    // Handle file upload if present
    let attachment_id: number | null = null;
    const file = (req as any).files?.messageFiles?.[0];
    if (file) {
      const fileMetadata = uploadService.getFileMetadata(
        file,
        ticket.organization_id,
        created_by
      );
      if (fileMetadata) {
        const itemData = {
          ...fileMetadata,
          item_category: item_category.MESSAGE_ATTACHMENT,
        };
        const item = await Item.create(itemData as any);
        attachment_id = item.id;
      }
    }

    // Create message
    const message = await TicketMessage.create({
      ticket_id: Number(ticket_id),
      message_text,
      message_type,
      is_private,
      attachment_id,
      created_by,
    });

    // Update first_response_at if this is the first agent response
    if (message_type === MessageType.AGENT && !ticket.first_response_at) {
      await ticket.update({
        first_response_at: new Date(),
        updated_by: created_by,
      });

      // Create history record for first response
      await TicketHistory.create({
        ticket_id: ticket.id,
        action_type: "MESSAGE_ADDED",
        previous_status: ticket.ticket_status,
        new_status: ticket.ticket_status,
        change_note: "First response provided by agent",
        created_by,
      });
    }

    // Auto-update ticket status if needed
    if (
      message_type === MessageType.AGENT &&
      ticket.ticket_status === TicketStatus.OPEN
    ) {
      await ticket.update({
        ticket_status: TicketStatus.IN_PROGRESS,
        updated_by: created_by,
      });

      await TicketHistory.create({
        ticket_id: ticket.id,
        action_type: "STATUS_CHANGED",
        previous_status: TicketStatus.OPEN,
        new_status: TicketStatus.IN_PROGRESS,
        change_note: "Status updated to In Progress after agent response",
        created_by,
      });
    }

    // Send email notification for new message (using RabbitMQ)
    try {
      const messageFromUser = await getUser(created_by);
      const isAgentResponse = message_type === MessageType.AGENT;

      await notificationService.sendSupportTicketNotification(
        ticket.ticket_owner_email,
        "TICKET_MESSAGE_ADDED",
        {
          ticketNumber: ticket.ticket_number,
          submitter_name: ticket.ticket_owner_name,
          subject: ticket.ticket_title,
          message_text: message_text,
          message_from: messageFromUser?.name || "Unknown User",
          is_agent_response: isAgentResponse,
        }
      );
    } catch (emailError) {
      console.error("Failed to send message notification:", emailError);
      // Don't fail the message creation if email fails
    }

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: res.__("MESSAGE_ADDED_SUCCESSFULLY"),
      data: message,
    });
  } catch (error) {
    console.error("Error adding message:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("MESSAGE_ADD_FAILED"),
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * @description Get messages for a ticket
 * @route GET /api/v1/private/tickets/:ticket_id/messages
 * @access Private (requires authentication)
 */
const getTicketMessages = async (req: Request, res: Response): Promise<any> => {
  try {
    const { ticket_id } = req.params;
    const { include_private, page, limit } = req.query;

    // Get pagination parameters using utility function
    const pagination = getPagination(page as string, limit as string);
    const isPaginated = !!(page || limit);

    const whereClause: any = { ticket_id: Number(ticket_id) };

    if (include_private !== "true") {
      whereClause.is_private = false;
    }

    // Build query options
    const queryOptions: any = {
      where: whereClause,
      order: [["created_at", "ASC"]],
    };

    // Only add pagination if parameters provided
    if (isPaginated && pagination.limit) {
      queryOptions.limit = pagination.limit;
      queryOptions.offset = pagination.offset;
    }

    const { rows: messages, count } =
      await TicketMessage.findAndCountAll(queryOptions);

    // Format response using utility function
    let responseData: any;
    if (isPaginated) {
      responseData = getPaginatedItems(
        { count, rows: messages },
        page as string,
        pagination.limit!
      );
    } else {
      responseData = {
        messages,
        total_records: count,
      };
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("MESSAGES_FETCHED_SUCCESSFULLY"),
      data: responseData,
    });
  } catch (error) {
    console.error("Error fetching messages:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("MESSAGES_FETCH_FAILED"),
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

//  - default export with object
export default {
  addMessage,
  getTicketMessages,
};
