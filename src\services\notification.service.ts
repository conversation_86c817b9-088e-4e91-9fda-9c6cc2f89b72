/**
 * Notification Service using RabbitMQ (following auth-ms pattern)
 * This service publishes email notifications to RabbitMQ queues
 * The actual email sending is handled by a separate email service
 */

import { RABBITMQ_QUEUE } from "../utils/constant";
import rabbitmq from "../rabbitmq/rabbitmq";

// Email templates for support notifications
export const SUPPORT_EMAIL_TEMPLATES = Object.freeze({
  TICKET_CREATED: {
    subject: "Support Ticket Created - #{ticket_number}",
    template: "ticket_created",
  },
  TICKET_STATUS_UPDATED: {
    subject: "Support Ticket Status Updated - #{ticket_number}",
    template: "ticket_status_updated",
  },
  TICKET_ASSIGNED: {
    subject: "Support Ticket Assigned - #{ticket_number}",
    template: "ticket_assigned",
  },
  TICKET_MESSAGE_ADDED: {
    subject: "New Message on Support Ticket - #{ticket_number}",
    template: "ticket_message_added",
  },
  TICKET_RESOLVED: {
    subject: "Support Ticket Resolved - #{ticket_number}",
    template: "ticket_resolved",
  },
});

/**
 * Development-only notification service
 * Logs notifications instead of sending actual emails
 */

interface NotificationData {
  ticket_number: string;
  submitter_email: string;
  submitter_name: string;
  subject: string;
  previous_status?: string;
  new_status?: string;
  resolution_note?: string;
  assigned_to?: string;
  organization_id?: string;
}

/**
 * Send ticket created notification via RabbitMQ
 */
export const sendTicketCreatedNotification = async (
  data: NotificationData
): Promise<void> => {
  try {
    const emailData = {
      to: data.submitter_email,
      subject: `Support Ticket Created - ${data.ticket_number}`,
      template: "ticket_created",
      data: {
        ticket_number: data.ticket_number,
        submitter_name: data.submitter_name,
        subject: data.subject,
        organization_id: data.organization_id,
      },
      timestamp: new Date().toISOString(),
    };

    // Publish to RabbitMQ email queue (following auth-ms pattern)
    await rabbitmq.publishMessage(RABBITMQ_QUEUE.MAIL_QUEUE, emailData);

    console.log(
      `📧 Ticket created notification queued for ${data.submitter_email}`
    );
  } catch (error) {
    console.error("Failed to queue ticket created notification:", error);
    // Don't throw error to avoid breaking ticket creation
  }
};

/**
 * Send ticket status updated notification via RabbitMQ
 */
export const sendTicketStatusUpdatedNotification = async (
  data: NotificationData
): Promise<void> => {
  try {
    const emailData = {
      to: data.submitter_email,
      subject: `Support Ticket Status Updated - ${data.ticket_number}`,
      template: "ticket_status_updated",
      data: {
        ticket_number: data.ticket_number,
        previous_status: data.previous_status,
        new_status: data.new_status,
        resolution_note: data.resolution_note,
        submitter_name: data.submitter_name,
      },
      timestamp: new Date().toISOString(),
    };

    await rabbitmq.publishMessage(RABBITMQ_QUEUE.MAIL_QUEUE, emailData);

    console.log(
      `📧 Ticket status updated notification queued for ${data.submitter_email}`
    );
  } catch (error) {
    console.error("Failed to queue ticket status updated notification:", error);
  }
};

/**
 * Send ticket assigned notification via RabbitMQ
 */
export const sendTicketAssignedNotification = async (
  data: NotificationData
): Promise<void> => {
  try {
    const emailData = {
      to: data.submitter_email,
      subject: `Support Ticket Assigned - ${data.ticket_number}`,
      template: "ticket_assigned",
      data: {
        ticket_number: data.ticket_number,
        assigned_to: data.assigned_to,
        submitter_name: data.submitter_name,
      },
      timestamp: new Date().toISOString(),
    };

    await rabbitmq.publishMessage(RABBITMQ_QUEUE.MAIL_QUEUE, emailData);

    console.log(
      `📧 Ticket assigned notification queued for ${data.submitter_email}`
    );
  } catch (error) {
    console.error("Failed to queue ticket assigned notification:", error);
  }
};

/**
 * Send ticket resolved notification via RabbitMQ
 */
export const sendTicketResolvedNotification = async (
  data: NotificationData
): Promise<void> => {
  try {
    const emailData = {
      to: data.submitter_email,
      subject: `Support Ticket Resolved - ${data.ticket_number}`,
      template: "ticket_resolved",
      data: {
        ticket_number: data.ticket_number,
        resolution_note: data.resolution_note,
        submitter_name: data.submitter_name,
      },
      timestamp: new Date().toISOString(),
    };

    await rabbitmq.publishMessage(RABBITMQ_QUEUE.MAIL_QUEUE, emailData);

    console.log(
      `📧 Ticket resolved notification queued for ${data.submitter_email}`
    );
  } catch (error) {
    console.error("Failed to queue ticket resolved notification:", error);
  }
};

/**
 * Generic support ticket notification function (for backward compatibility)
 */
export const sendSupportTicketNotification = async (
  to: string,
  templateName: string,
  data: any
): Promise<void> => {
  try {
    const emailData = {
      to: to,
      subject: `Support Ticket Notification - ${templateName}`,
      template: templateName.toLowerCase(),
      data: {
        ...data,
        recipient_email: to,
      },
      timestamp: new Date().toISOString(),
    };

    // Publish to RabbitMQ email queue (following auth-ms pattern)
    await rabbitmq.publishMessage(RABBITMQ_QUEUE.MAIL_QUEUE, emailData);

    console.log(
      `📧 Support ticket notification queued: ${templateName} for ${to}`
    );
  } catch (error) {
    console.error("Failed to queue support ticket notification:", error);
    // Don't throw error to avoid breaking ticket operations
  }
};

// Default export for backward compatibility
export default {
  sendTicketCreatedNotification,
  sendTicketStatusUpdatedNotification,
  sendTicketAssignedNotification,
  sendTicketResolvedNotification,
  sendSupportTicketNotification,
};
