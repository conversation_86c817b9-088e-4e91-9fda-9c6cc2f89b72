import axios from "axios";
import { db } from "../models";

const Item = db.Item;

/**
 * Keycloak Common Functions - Following Auth Microservice Pattern
 * This file contains all Keycloak integration functions used across the customer service microservice
 */

/**
 * Get Keycloak Admin Token
 * Same pattern as auth microservice
 */
const getKeycloakAdminToken = async () => {
  try {
    const tokenUrl = `${global.config.KEYCLOAK_SERVER_URL}realms/${global.config.KEYCLOAK_REALM_NAME}/protocol/openid-connect/token`;

    const response = await axios.post(
      tokenUrl,
      new URLSearchParams({
        grant_type: "client_credentials",
        client_id: global.config.KEYCLOAK_CLIENT_ID,
        client_secret: global.config.KEYCLOAK_SECRET_KEY,
      }),
      {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      }
    );

    return response.data.access_token;
  } catch (e: any) {
    console.log("Keycloak Admin Token Exception: ", e);
    return null;
  }
};

/**
 * Make HTTP Request
 * Same pattern as auth microservice
 */
const makeRequest = async (
  url: any,
  method: any,
  payload: any = null,
  headers: any = null
) => {
  try {
    const config: any = {
      method,
      url,
      headers: headers,
    };
    if (payload) {
      config.data = payload;
    }

    const response = await axios(config);
    return response;
  } catch (e: any) {
    console.log("axios Exception: ", e);
    if (e.response && e.response.status !== 200) {
      return {
        status: false,
        statusCode: e.response.status,
        message: e.response.data.error_description
          ? e.response.data.error_description
          : e.response.data.errorMessage,
        statusText: e.response.statusText,
        field: e.response.data?.field,
      };
    }
  }
  // Ensure a return value for all code paths
  return {
    status: false,
    statusCode: 500,
    message: "Unknown error in makeRequest",
    statusText: "Error",
  };
};

/**
 * Get Data from Keycloak URL
 * Same pattern as auth microservice with organization logo handling
 */
const getData = async (url: any, token: any = null) => {
  try {
    token = token ? token : await getKeycloakAdminToken();

    const response: any = await makeRequest(url, "GET", null, {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    });

    if (response.status == false) {
      return {
        status: response.status,
        statusText: response.statusText,
        statusCode: response.statusCode,
        message: response.message,
      };
    }

    if (response.data && response.data.attributes) {
      const attributes = response.data.attributes;
      // Transform the attributes by converting array values to single values
      const transformedAttributes: any = {};
      Object.keys(attributes).forEach((key) => {
        // Take the first element of the array or keep the value if it's already a single value
        transformedAttributes[key] = Array.isArray(attributes[key])
          ? attributes[key][0]
          : attributes[key];
      });

      // Update the response with the transformed attributes
      response.data.attributes = transformedAttributes;

      // Handle organization logo same as auth microservice
      if (
        response.data.attributes.organization_logo &&
        response.data.attributes.organization_logo.trim() !== "" &&
        response.data.attributes.organization_logo !== '""'
      ) {
        if (!isNaN(response.data.attributes.organization_logo)) {
          // If it's a number, it's an Item ID - fetch from database
          const getItem = await Item.findOne({
            where: { id: response.data.attributes.organization_logo },
          });
          if (getItem) {
            response.data.attributes.organization_logo =
              global.config.API_BASE_URL + getItem?.item_location;
          }
        } else {
          // If it's a string, it's a file path
          response.data.attributes.organization_logo =
            global.config.API_BASE_URL +
            `organization_images/` +
            response.data.attributes.organization_logo;
        }
      }
    }

    return {
      status: true,
      message: response,
      statusText: response.statusText,
      data: response.data,
    };
  } catch (e: any) {
    console.log("Get data By Id Exception: ", e);
    if (e.response && e.response.status !== 200) {
      return {
        status: false,
        statusCode: e.response.status,
        message: e.response.data.errorMessage,
        statusText: e.response.statusText,
      };
    }
  }
  // Ensure a return value for all code paths
  return {
    status: false,
    statusCode: 500,
    message: "Unknown error in getData",
    statusText: "Error",
  };
};

/**
 * Get Organization By ID
 * Same pattern as auth microservice
 */
const getOrganizationById = async (orgId: string, token: any = null) => {
  try {
    // Make URL for get organization data
    const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/organizations/${orgId}`;

    // Fetch organization data
    let getOrgData: any = await getData(keycloakRealmUrl, token);

    if (getOrgData.status == false) {
      return {
        status: getOrgData.status,
        statusCode: getOrgData.statusCode,
        message: getOrgData.message,
        data: getOrgData.statusText,
      };
    }

    getOrgData = getOrgData?.data;

    // Check if organization name contains _ then update with space
    if (getOrgData.name && getOrgData.name.includes("_")) {
      // Replace underscore with space
      getOrgData.name = getOrgData.name.replace(/_/g, " ");
      // Set alias same as name, because both are same
      getOrgData.alias = getOrgData.name;
    }

    return {
      status: true,
      message: "Organization data fetched successfully",
      data: getOrgData,
    };
  } catch (e: any) {
    console.log("Exception: ", e);
    return {
      status: false,
      statusCode: 500,
      message: "Something went wrong",
      data: e,
    };
  }
};

/**
 * Validate Support PIN for Organization
 * Custom function for customer service microservice
 */
const validateSupportPin = async (
  organizationId: string,
  providedPin: string,
  token: any = null
) => {
  try {
    const orgResult = await getOrganizationById(organizationId, token);

    if (!orgResult.status) {
      return {
        isValid: false,
        error: "Organization not found",
        organization: null,
      };
    }

    const organization = orgResult.data;

    if (!organization.enabled) {
      return {
        isValid: false,
        error: "Organization is disabled",
        organization: null,
      };
    }

    // Get support PIN from attributes
    const supportPin = organization.attributes?.support_pin;

    if (!supportPin) {
      return {
        isValid: false,
        error: "Support PIN not configured for this organization",
        organization: null,
      };
    }

    // Validate PIN (plain text comparison)
    const isValid = providedPin === supportPin;

    return {
      isValid,
      organization,
      error: isValid ? undefined : "Invalid support PIN",
    };
  } catch (error: any) {
    console.error("Error validating support PIN:", error);
    return {
      isValid: false,
      error: "Failed to validate support PIN",
      organization: null,
    };
  }
};

export {
  getKeycloakAdminToken,
  makeRequest,
  getData,
  getOrganizationById,
  validateSupportPin,
};
