import { Request, Response, NextFunction } from "express";
import { StatusCodes } from "http-status-codes";
import { isDefaultAccess } from "../utils/common";

/**
 * Admin authentication middleware
 * Checks if user has super admin access following TTH patterns
 */
export const adminAuth = async (req: Request, res: Response, next: NextFunction): Promise<any> => {
  try {
    const user = (req as any).user;

    if (!user) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: "Authentication required",
        error: "No user found in request"
      });
    }

    // Check if user has super admin access using TTH pattern
    const hasAdminAccess = await isDefaultAccess(user.keycloak_userId);

    if (!hasAdminAccess) {
      return res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: "Admin access required",
        error: "Insufficient permissions"
      });
    }

    // Add admin flag to user object for easy access in controllers
    (req as any).user.isAdmin = true;
    next();

  } catch (error) {
    console.error("Admin authentication error:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: "Admin authentication failed",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
};

/**
 * Agent authentication middleware
 * Checks if user has agent or admin access
 */
export const agentAuth = async (req: Request, res: Response, next: NextFunction): Promise<any> => {
  try {
    const user = (req as any).user;

    if (!user) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: "Authentication required",
        error: "No user found in request"
      });
    }

    // Check if user has admin access
    const hasAdminAccess = await isDefaultAccess(user.keycloak_userId);

    if (hasAdminAccess) {
      (req as any).user.isAdmin = true;
      (req as any).user.isAgent = true;
      return next();
    }

    // For now, allow any authenticated user to be an agent
    // In production, you would check specific agent roles here
    (req as any).user.isAgent = true;
    next();

  } catch (error) {
    console.error("Agent authentication error:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: "Agent authentication failed",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
};

/**
 * Get effective organization ID for queries
 * Admin users can specify organization_id in query params or get all data
 * Regular users are restricted to their organization
 */
export const getEffectiveOrganizationId = async (user: any, queryOrganizationId?: string): Promise<string | null | undefined> => {
  try {
    const hasAdminAccess = await isDefaultAccess(user.keycloak_userId);

    if (hasAdminAccess) {
      // Admin can specify organization_id in query, or get all data if not specified
      if (queryOrganizationId !== undefined) {
        return queryOrganizationId === 'null' || queryOrganizationId === '' ? null : queryOrganizationId;
      }
      // If no organization specified in query, return undefined to get all data
      return undefined;
    } else {
      // Regular users are restricted to their organization
      return user.organization_id;
    }
  } catch (error) {
    console.error("Error getting effective organization ID:", error);
    return user.organization_id; // Fallback to user's organization
  }
};

/**
 * Check if user has admin access
 */
export const isAdminUser = async (user: any): Promise<boolean> => {
  try {
    return await isDefaultAccess(user.keycloak_userId);
  } catch (error) {
    console.error("Error checking admin access:", error);
    return false;
  }
};
