import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

interface TicketHistoryAttributes {
  id?: number;
  ticket_id: number;
  action_type: string;
  previous_status?: string;
  new_status?: string;
  previous_priority?: string;
  new_priority?: string;
  previous_assigned_to?: number;
  new_assigned_to?: number;
  field_changed?: string;
  old_value?: string;
  new_value?: string;
  change_note?: string;
  change_reason?: string;
  ip_address?: string;
  user_agent?: string;
  created_by: number;
  created_at?: Date;
}

export default class TicketHistory
  extends Model<TicketHistoryAttributes>
  implements TicketHistoryAttributes
{
  public id!: number;
  public ticket_id!: number;
  public action_type!: string;
  public previous_status?: string;
  public new_status?: string;
  public previous_priority?: string;
  public new_priority?: string;
  public previous_assigned_to?: number;
  public new_assigned_to?: number;
  public field_changed?: string;
  public old_value?: string;
  public new_value?: string;
  public change_note?: string;
  public change_reason?: string;
  public ip_address?: string;
  public user_agent?: string;
  public created_by!: number;
  public readonly created_at!: Date;

  static associate(models: any) {
    // TicketHistory belongs to Ticket (proper Sequelize association)
    TicketHistory.belongsTo(models.Ticket, {
      foreignKey: "ticket_id",
      as: "ticket",
    });

    // Soft reference to nv_users table (no Sequelize association):
    // - created_by references nv_users.id (user who made the change)
    // This maintains referential integrity without model dependencies
    // User data will be fetched via raw queries or separate service calls when needed
  }
}

// Initialize the model
TicketHistory.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    ticket_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "mo_support_tickets",
        key: "id",
      },
      comment: "Reference to the support ticket",
    },
    action_type: {
      type: DataTypes.ENUM(
        "CREATED",
        "STATUS_CHANGED",
        "PRIORITY_CHANGED",
        "ASSIGNED",
        "UNASSIGNED",
        "MESSAGE_ADDED",
        "ATTACHMENT_ADDED",
        "ATTACHMENT_REMOVED",
        "ESCALATED",
        "RESOLVED",
        "CLOSED",
        "REOPENED",
        "UPDATED",
        "DELETED"
      ),
      allowNull: false,
      comment: "Type of action performed",
    },
    previous_status: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: "Previous status before change",
    },
    new_status: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: "New status after change",
    },
    previous_priority: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: "Previous priority before change",
    },
    new_priority: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: "New priority after change",
    },
    previous_assigned_to: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: "Previous assigned user ID",
    },
    new_assigned_to: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: "New assigned user ID",
    },
    field_changed: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: "Name of the field that was changed",
    },
    old_value: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: "Previous value of the changed field",
    },
    new_value: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: "New value of the changed field",
    },
    change_note: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: "Reason or context for the change",
    },
    change_reason: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "Specific reason for the change",
    },
    ip_address: {
      type: DataTypes.STRING(45),
      allowNull: true,
      comment: "IP address of the user making the change",
    },
    user_agent: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: "User agent string of the client",
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "nv_users",
        key: "id",
      },
      comment: "ID of user who made the change",
    },
  },
  {
    sequelize,
    tableName: "mo_support_ticket_history",
    modelName: "TicketHistory",
    timestamps: true,
    paranoid: false, // No soft delete for history records
    underscored: true,
    createdAt: "created_at",
    updatedAt: false, // History records are immutable
    indexes: [
      {
        fields: ["ticket_id"],
      },
      {
        fields: ["created_by"],
      },
      {
        fields: ["created_at"],
      },
      {
        fields: ["new_status"],
      },
    ],
  }
);
