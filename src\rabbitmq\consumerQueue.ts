import rabbitMQ from "./rabbitmq";
import { RABBITMQ_QUEUE } from "../utils/constant";

export const setupConsumers = async () => {
  try {
    console.log("Setting up RabbitMQ consumers for customer support...");

    // Generic handler for message processing
    const logHandler = async (msg: any, routingKey: string) => {
      // Process message based on routing key
      // TODO: Implement specific handlers for each message type
    };

    // Set up email and notification queues
    await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.MAIL_QUEUE, logHandler);
    await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.MAIL_FAILED, logHandler);
    await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.MAIL_SUCCESS, logHandler);
    await rabbitMQ.consumeMessage(
      RABBITMQ_QUEUE.SUPPORT_TICKET_EMAIL,
      logHandler
    );
    await rabbitMQ.consumeMessage(
      RABBITMQ_QUEUE.SUPPORT_NOTIFICATION,
      logHandler
    );

    // Set up ticket update queues
    await rabbitMQ.consumeMessage(
      RABBITMQ_QUEUE.TICKET_STATUS_UPDATE,
      logHandler
    );
    await rabbitMQ.consumeMessage(
      RABBITMQ_QUEUE.TICKET_ASSIGNMENT_UPDATE,
      logHandler
    );

    console.log(
      "RabbitMQ consumers for customer support are set up successfully."
    );
  } catch (error) {
    console.error(
      "Error setting up RabbitMQ consumers for customer support:",
      error
    );
    // Re-throw the error to be handled by the caller
    throw error;
  }
};
