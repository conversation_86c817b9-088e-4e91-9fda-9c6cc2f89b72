version: '3.8'

services:
  # Customer Service Microservice
  customer-service:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "5008:5008"
    environment:
      - NODE_ENV=development
      - PORT=5008
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=customer_service_db
      - DB_USER=root
      - DB_PASSWORD=password
      - JWT_SECRET=your_jwt_secret_here
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_MODEL=gpt-4
      - RABBITMQ_URL=amqp://admin:password@rabbitmq:5672
      - REDIS_URL=redis://redis:6379
      - CORS_ORIGIN=*
    depends_on:
      - mysql
      - redis
      - rabbitmq
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    networks:
      - customer-service-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5008/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MySQL Database
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=customer_service_db
      - MYSQL_USER=customer_user
      - MYSQL_PASSWORD=customer_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - customer-service-network
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - customer-service-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # RabbitMQ for message queuing
  rabbitmq:
    image: rabbitmq:3-management-alpine
    environment:
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=password
    ports:
      - "5672:5672"
      - "15672:15672"  # Management UI
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - customer-service-network
    restart: unless-stopped

  # Nginx for load balancing (optional)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/ssl:/etc/nginx/ssl
    depends_on:
      - customer-service
    networks:
      - customer-service-network
    restart: unless-stopped

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - customer-service-network
    restart: unless-stopped
    profiles:
      - monitoring

  # Grafana for visualization (optional)
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./docker/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - customer-service-network
    restart: unless-stopped
    profiles:
      - monitoring

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  rabbitmq_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  customer-service-network:
    driver: bridge
