import { Request, Response, NextFunction } from "express";
import <PERSON><PERSON> from "joi";
import { StatusCodes } from "http-status-codes";
import { isCelebrateError } from "celebrate";
import { VALIDATION } from "../utils/constant";

// Following recipe microservice validation patterns

/**
 * Generic validation middleware factory
 */
const validate = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction): any => {
    const { error } = schema.validate(req.body, { abortEarly: false });

    if (error) {
      const errorDetails = error.details.map((detail) => ({
        field: detail.path.join("."),
        message: detail.message,
        value: detail.context?.value,
      }));

      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("VALIDATION_ERROR") || "Validation failed",
        errors: errorDetails,
      });
    }

    next();
  };
};

/**
 * Ticket creation validation schema
 */
const ticketCreationSchema = Joi.object({
  ticket_owner_name: Joi.string().min(2).max(100).required().messages({
    "string.empty": "Ticket owner name is required",
    "string.min": "Ticket owner name must be at least 2 characters",
    "string.max": "Ticket owner name cannot exceed 100 characters",
  }),

  ticket_owner_email: Joi.string().email().required().messages({
    "string.email": "Please provide a valid email address",
    "string.empty": "Email is required",
  }),

  ticket_owner_phone: Joi.string()
    .pattern(VALIDATION.PHONE_REGEX)
    .optional()
    .allow("")
    .messages({
      "string.pattern.base": "Please provide a valid phone number",
    }),

  ticket_title: Joi.string()
    .min(VALIDATION.TICKET_TITLE_MIN_LENGTH)
    .max(VALIDATION.TICKET_TITLE_MAX_LENGTH)
    .required()
    .messages({
      "string.empty": "Ticket title is required",
      "string.min": `Ticket title must be at least ${VALIDATION.TICKET_TITLE_MIN_LENGTH} characters`,
      "string.max": `Ticket title cannot exceed ${VALIDATION.TICKET_TITLE_MAX_LENGTH} characters`,
    }),

  ticket_description: Joi.string()
    .min(VALIDATION.TICKET_DESCRIPTION_MIN_LENGTH)
    .max(VALIDATION.TICKET_DESCRIPTION_MAX_LENGTH)
    .required()
    .messages({
      "string.empty": "Ticket description is required",
      "string.min": `Ticket description must be at least ${VALIDATION.TICKET_DESCRIPTION_MIN_LENGTH} characters`,
      "string.max": `Ticket description cannot exceed ${VALIDATION.TICKET_DESCRIPTION_MAX_LENGTH} characters`,
    }),

  ticket_module: Joi.string()
    .valid("hrms", "pms", "recipe", "other")
    .default("other")
    .messages({
      "any.only": "Ticket module must be one of: hrms, pms, recipe, other",
    }),

  ticket_type: Joi.string()
    .valid(
      "bug",
      "feature_request",
      "general_query",
      "technical",
      "non_technical",
      "export_help",
      "support"
    )
    .default("general_query")
    .messages({
      "any.only":
        "Ticket type must be one of: bug, feature_request, general_query, technical, non_technical, export_help, support",
    }),

  ticket_priority: Joi.string()
    .valid("none", "low", "medium", "high", "urgent", "emergency")
    .default("medium")
    .messages({
      "any.only":
        "Ticket priority must be one of: none, low, medium, high, urgent, emergency",
    }),

  ticket_visibility: Joi.string()
    .valid("public", "private")
    .default("private")
    .messages({
      "any.only": "Ticket visibility must be either public or private",
    }),

  organization_id: Joi.string().optional().allow("").max(100).messages({
    "string.max": "Organization ID cannot exceed 100 characters",
  }),
});

/**
 * Ticket update validation schema
 */
const ticketUpdateSchema = Joi.object({
  ticket_status: Joi.string()
    .valid(
      "open",
      "in_progress",
      "escalated",
      "qa_review",
      "under_review",
      "on_hold",
      "resolved",
      "closed",
      "invoiced",
      "assigned"
    )
    .optional()
    .messages({
      "any.only": "Invalid ticket status",
    }),

  ticket_priority: Joi.string()
    .valid("none", "low", "medium", "high", "urgent", "emergency")
    .optional()
    .messages({
      "any.only": "Invalid ticket priority",
    }),

  assigned_to_user_id: Joi.number()
    .integer()
    .positive()
    .optional()
    .allow(null)
    .messages({
      "number.base": "Assigned user ID must be a number",
      "number.positive": "Assigned user ID must be positive",
    }),

  resolution_summary: Joi.string().max(2000).optional().allow("").messages({
    "string.max": "Resolution summary cannot exceed 2000 characters",
  }),

  customer_satisfaction_rating: Joi.number()
    .integer()
    .min(1)
    .max(5)
    .optional()
    .messages({
      "number.min": "Rating must be between 1 and 5",
      "number.max": "Rating must be between 1 and 5",
    }),

  customer_feedback: Joi.string().max(1000).optional().allow("").messages({
    "string.max": "Customer feedback cannot exceed 1000 characters",
  }),
});

/**
 * Message validation schema
 */
const messageSchema = Joi.object({
  ticket_id: Joi.string().uuid().required().messages({
    "string.empty": "Ticket ID is required",
    "string.uuid": "Invalid ticket ID format",
  }),

  message_content: Joi.string()
    .min(VALIDATION.MESSAGE_MIN_LENGTH)
    .max(VALIDATION.MESSAGE_MAX_LENGTH)
    .required()
    .messages({
      "string.empty": "Message content is required",
      "string.min": `Message must be at least ${VALIDATION.MESSAGE_MIN_LENGTH} character`,
      "string.max": `Message cannot exceed ${VALIDATION.MESSAGE_MAX_LENGTH} characters`,
    }),

  message_visibility: Joi.string()
    .valid("public", "private", "internal")
    .default("public")
    .messages({
      "any.only":
        "Message visibility must be one of: public, private, internal",
    }),

  parent_message_id: Joi.string().uuid().optional().allow(null).messages({
    "string.uuid": "Invalid parent message ID format",
  }),

  edit_reason: Joi.string().max(255).optional().allow("").messages({
    "string.max": "Edit reason cannot exceed 255 characters",
  }),
});

/**
 * Chat message validation schema
 */
const chatMessageSchema = Joi.object({
  message: Joi.string().min(1).max(2000).required().messages({
    "string.empty": "Message is required",
    "string.max": "Message cannot exceed 2000 characters",
  }),

  user_name: Joi.string().min(2).max(100).optional().messages({
    "string.min": "Name must be at least 2 characters",
    "string.max": "Name cannot exceed 100 characters",
  }),

  user_email: Joi.string().email().optional().messages({
    "string.email": "Please provide a valid email address",
  }),

  ticket_id: Joi.string().uuid().optional().messages({
    "string.uuid": "Invalid ticket ID format",
  }),
});

/**
 * Escalation validation schema
 */
const escalationSchema = Joi.object({
  reason: Joi.string().min(10).max(500).required().messages({
    "string.empty": "Escalation reason is required",
    "string.min": "Escalation reason must be at least 10 characters",
    "string.max": "Escalation reason cannot exceed 500 characters",
  }),
});

/**
 * Assignment validation schema
 */
const assignmentSchema = Joi.object({
  assigned_to_user_id: Joi.number().integer().positive().required().messages({
    "number.base": "Assigned user ID must be a number",
    "number.positive": "Assigned user ID must be positive",
    "any.required": "Assigned user ID is required",
  }),
});

/**
 * Rating validation schema
 */
const ratingSchema = Joi.object({
  rating: Joi.number().integer().min(1).max(5).required().messages({
    "number.base": "Rating must be a number",
    "number.min": "Rating must be between 1 and 5",
    "number.max": "Rating must be between 1 and 5",
    "any.required": "Rating is required",
  }),

  feedback: Joi.string().max(500).optional().allow("").messages({
    "string.max": "Feedback cannot exceed 500 characters",
  }),
});

// Export validation middlewares
export const validateTicketCreation = validate(ticketCreationSchema);
export const validateTicketUpdate = validate(ticketUpdateSchema);
export const validateMessage = validate(messageSchema);
export const validateChatMessage = validate(chatMessageSchema);
export const validateEscalation = validate(escalationSchema);
export const validateAssignment = validate(assignmentSchema);
export const validateRating = validate(ratingSchema);

/**
 * Celebrate error handler ()
 */
const HandleErrorMessage = async (err: any, _req: any, res: any, next: any) => {
  try {
    if (isCelebrateError(err)) {
      let errorDetails: any = null;

      // Check different sources of validation errors
      if (err.details.get("body")) {
        errorDetails = err.details.get("body");
      } else if (err.details.get("query")) {
        errorDetails = err.details.get("query");
      } else if (err.details.get("params")) {
        errorDetails = err.details.get("params");
      } else if (err.details.get("headers")) {
        errorDetails = err.details.get("headers");
      }

      if (errorDetails) {
        let mainMessage = "Validation failed";

        if (errorDetails.details && errorDetails.details.length > 0) {
          // Get the first error message and clean it up
          mainMessage = errorDetails.details[0].message
            .replace(/"/g, "") // Remove quotes
            .replace(/\\/g, "") // Remove backslashes
            .replace(/^\w+\s/, "") // Remove leading validation type
            .trim();
        }

        return res.status(400).json({
          status: false,
          message: mainMessage,
        });
      }

      // Fallback for celebrate errors without detailed information
      return res.status(400).json({
        status: false,
        message: "Invalid request data",
      });
    }

    // If it's not a celebrate error, pass it to the next error handler
    next(err);
  } catch (e: any) {
    console.error("Error in validation middleware:", e);
    return res.status(400).json({
      status: false,
      message: "Validation error occurred",
    });
  }
};

export { HandleErrorMessage };

export default {
  validateTicketCreation,
  validateTicketUpdate,
  validateMessage,
  validateChatMessage,
  validateEscalation,
  validateAssignment,
  validateRating,
};
