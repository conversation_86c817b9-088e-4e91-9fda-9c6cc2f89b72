import express from "express";

// Import route modules (following project structure)
import privateRoutes from "./private";
import publicRoutes from "./public";

const router = express.Router();

// Health check endpoint (available without authentication)
router.get("/health", (req, res) => {
  res.status(200).json({
    status: "OK",
    service: "Customer Service Microservice",
    version: "1.0.0",
    timestamp: new Date().toISOString(),
    features: {
      support_tickets: true,
      pin_verification: true,
      file_attachments: true,
      organization_scoped: true,
      email_notifications: true,
      authentication_required: true,
      private_microservice: true,
    },
  });
});

// Export organized routes (following project structure)
export { privateRoutes, publicRoutes };
export default router;
