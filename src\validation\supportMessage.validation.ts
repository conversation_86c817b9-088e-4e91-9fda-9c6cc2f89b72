import { celebrate, Joi, Segments } from "celebrate";

// Validation for adding message to ticket
export const addMessageValidation = celebrate({
  [Segments.PARAMS]: Joi.object({
    ticket_id: Joi.number().integer().required(),
  }),
  [Segments.BODY]: Joi.object({
    message_text: Joi.string().required().min(1).max(10000),
    message_type: Joi.string()
      .valid("USER", "AGENT", "SYSTEM", "INTERNAL_NOTE")
      .default("USER"),
    is_private: Joi.boolean().default(false),
    attachment_id: Joi.number().integer().optional(),
  }),
});

// Validation for getting ticket messages
export const getTicketMessagesValidation = celebrate({
  [Segments.PARAMS]: Joi.object({
    ticket_id: Joi.number().integer().required(),
  }),
  [Segments.QUERY]: Joi.object({
    include_private: Joi.boolean().default(false),
    page: Joi.number().integer().min(1).optional(),
    limit: Joi.number().integer().min(1).max(100).optional(),
    message_type: Joi.string()
      .valid("USER", "AGENT", "SYSTEM", "INTERNAL_NOTE")
      .optional(),
    since: Joi.date().iso().optional(),
  }),
});

// Validation for adding internal note
export const addInternalNoteValidation = celebrate({
  [Segments.PARAMS]: Joi.object({
    ticket_id: Joi.number().integer().required(),
  }),
  [Segments.BODY]: Joi.object({
    message_text: Joi.string().required().min(1).max(10000),
  }),
});

// Validation for updating message
export const updateMessageValidation = celebrate({
  [Segments.PARAMS]: Joi.object({
    id: Joi.number().integer().required(),
  }),
  [Segments.BODY]: Joi.object({
    message_text: Joi.string().optional().min(1).max(10000),
    is_private: Joi.boolean().optional(),
  }).min(1), // At least one field must be provided
});

// Validation for deleting message
export const deleteMessageValidation = celebrate({
  [Segments.PARAMS]: Joi.object({
    id: Joi.number().integer().required(),
  }),
});

// Validation for message search within ticket
export const searchMessagesValidation = celebrate({
  [Segments.PARAMS]: Joi.object({
    ticket_id: Joi.number().integer().required(),
  }),
  [Segments.QUERY]: Joi.object({
    q: Joi.string().required().min(3),
    include_private: Joi.boolean().default(false),
    message_type: Joi.string()
      .valid("USER", "AGENT", "SYSTEM", "INTERNAL_NOTE")
      .optional(),
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(50).default(20),
  }),
});

// Validation for bulk message operations
export const bulkMessageOperationValidation = celebrate({
  [Segments.BODY]: Joi.object({
    message_ids: Joi.array().items(Joi.number().integer()).min(1).required(),
    operation: Joi.string()
      .valid("delete", "toggle_private", "mark_read")
      .required(),
    data: Joi.object().when("operation", {
      is: "toggle_private",
      then: Joi.object({
        is_private: Joi.boolean().required(),
      }).required(),
      otherwise: Joi.object().optional(),
    }),
  }),
});

// Validation for message analytics
export const messageAnalyticsValidation = celebrate({
  [Segments.QUERY]: Joi.object({
    ticket_id: Joi.number().integer().optional(),
    organization_id: Joi.string().max(50).optional(),
    start_date: Joi.date().iso().optional(),
    end_date: Joi.date().iso().min(Joi.ref("start_date")).optional(),
    message_type: Joi.string()
      .valid("USER", "AGENT", "SYSTEM", "INTERNAL_NOTE")
      .optional(),
    group_by: Joi.string()
      .valid("message_type", "date", "user")
      .default("message_type"),
  }),
});

// Validation for message export
export const exportMessagesValidation = celebrate({
  [Segments.PARAMS]: Joi.object({
    ticket_id: Joi.number().integer().required(),
  }),
  [Segments.QUERY]: Joi.object({
    format: Joi.string().valid("json", "csv", "pdf").default("json"),
    include_private: Joi.boolean().default(false),
    include_attachments: Joi.boolean().default(false),
    start_date: Joi.date().iso().optional(),
    end_date: Joi.date().iso().min(Joi.ref("start_date")).optional(),
  }),
});
