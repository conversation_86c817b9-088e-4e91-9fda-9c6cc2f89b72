import { Router } from "express";
import supportConfigController from "../../controller/supportConfig.controller";
import { validatePinValidation } from "../../validation/supportConfig.validation";

const router = Router();

// Health check endpoint for public routes
router.get("/health", (req, res) => {
  res.status(200).json({
    status: "OK",
    service: "Customer Service Microservice - Public Routes",
    message:
      "Only PIN validation is available publicly. All other endpoints require authentication.",
    version: "1.0.0",
    timestamp: new Date().toISOString(),
    features: {
      public_ticket_creation: false,
      authentication_required: true,
      organization_scoped: true,
      support_pin_validation: true,
    },
  });
});

// PIN validation endpoint (public access for ticket creation flow)
router.post(
  "/config/:organization_id/validate-pin",
  validatePinValidation,
  supportConfigController.validatePin
);

// Redirect any other public requests to private routes
router.use("*", (req, res) => {
  res.status(401).json({
    status: false,
    message: "Authentication required. Please use /v1/private endpoints.",
    redirect: "/v1/private",
  });
});

export default router;
