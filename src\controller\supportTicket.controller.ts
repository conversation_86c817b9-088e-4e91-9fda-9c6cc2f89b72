/**
 * Support Ticket Controller
 *
 * This controller handles all ticket-related operations including:
 * - Ticket CRUD operations
 * - Message management
 * - Assignment and escalation

 * - Feedback collection
 *
 * Following the same patterns as the recipe microservice for consistency.
 *
 * <AUTHOR> Service Team
 * @version 1.0.0
 */

import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Op } from "sequelize";
import { db } from "../models";
import { TicketStatus, Priority, IssueType } from "../models/Ticket";
import { MessageType } from "../models/TicketMessage";
import uploadService from "../helper/upload.service";
import { getPaginatedItems, getPagination, getUser } from "../utils/common";
import { getAssignableUsers, isDefaultAccess } from "../utils/common";
import * as ExcelJS from "exceljs";
import * as notificationService from "../services/notification.service";
import { organizationHelper } from "../helper/organization.helper";

// Get models from db object to ensure associations are set up (following recipe pattern)
const Ticket = db.Ticket;
const TicketMessage = db.TicketMessage;
const TicketHistory = db.TicketHistory;
const TicketAttachment = db.TicketAttachment;
const Item = db.Item;

/**
 * @description Get all support tickets with pagination and filters
 * @route GET /api/v1/private/tickets
 * @access Private
 */
const getAllTickets = async (req: Request, res: Response): Promise<any> => {
  try {
    const {
      page,
      size,
      status,
      priority,
      assigned_to_user_id,
      organization_id,
      search,
      sort_by,
      sort_order,
      unassigned,
      download, // New parameter for export functionality
    } = req.query;

    const userId = (req as any).user?.id;
    const userOrganizationId = (req as any).user?.organization_id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS"),
      });
    }

    // Check if user has default access (super admin)
    const hasDefaultAccess = await isDefaultAccess(userId);

    // Determine effective organization ID following recipe microservice pattern
    let effectiveOrganizationId;
    if (hasDefaultAccess) {
      // Admin can specify organization_id in query, or get all data if not specified
      if (organization_id !== undefined) {
        effectiveOrganizationId =
          organization_id === "null" || organization_id === ""
            ? null
            : organization_id;
      }
      // If no organization specified, show all tickets (no filter)
    } else {
      // Regular users are restricted to their organization
      effectiveOrganizationId = userOrganizationId;
    }

    // Get pagination parameters - only apply if explicitly requested
    let limit: number | undefined;
    let offset: number | undefined;
    let isPaginated = false;

    if (page || size) {
      const pagination = getPagination(page as string, size as string);
      limit = pagination.limit;
      offset = pagination.offset;
      isPaginated = true;
    }

    const whereClause: any = {};

    // Organization filter
    if (effectiveOrganizationId !== undefined) {
      whereClause.organization_id = effectiveOrganizationId;
    }

    // Search functionality
    if (search) {
      whereClause[Op.or] = [
        { ticket_number: { [Op.like]: `%${search}%` } },
        { ticket_title: { [Op.like]: `%${search}%` } },
        { ticket_owner_name: { [Op.like]: `%${search}%` } },
        { ticket_owner_email: { [Op.like]: `%${search}%` } },
        { ticket_description: { [Op.like]: `%${search}%` } },
      ];
    }

    // Status filter
    if (status) {
      if (Array.isArray(status)) {
        whereClause.ticket_status = { [Op.in]: status };
      } else {
        whereClause.ticket_status = status;
      }
    }

    // Priority filter
    if (priority) {
      if (Array.isArray(priority)) {
        whereClause.ticket_priority = { [Op.in]: priority };
      } else {
        whereClause.ticket_priority = priority;
      }
    }

    // Assignment filter
    if (assigned_to_user_id === "null" || unassigned === "true") {
      whereClause.assigned_to_user_id = null;
    } else if (assigned_to_user_id) {
      whereClause.assigned_to_user_id = assigned_to_user_id;
    }

    // Build order clause
    const orderClause: any[] = [];
    if (sort_by && sort_order) {
      orderClause.push([sort_by as string, sort_order as string]);
    } else {
      orderClause.push(["created_at", "DESC"]);
    }

    // Check if download is requested
    if (download === "excel" || download === "csv") {
      // For export, we need all records without pagination
      const exportQueryOptions = {
        where: whereClause,
        include: [
          {
            model: TicketMessage,
            as: "messages",
            where: { is_private: false },
            required: false,
            limit: 1,
            order: [["created_at", "DESC"]],
          },
        ],
        order: orderClause,
        distinct: true,
        // Remove pagination for export
      };

      const { rows: allTickets, count: totalCount } =
        await Ticket.findAndCountAll(exportQueryOptions);

      return await handleTicketExport(
        req.query,
        allTickets,
        totalCount,
        download,
        res,
        effectiveOrganizationId
      );
    }

    // Build query options
    const queryOptions: any = {
      where: whereClause,
      include: [
        {
          model: TicketMessage,
          as: "messages",
          where: { is_private: false },
          required: false,
          limit: 1,
          order: [["created_at", "DESC"]],
        },
      ],
      order: orderClause,
      distinct: true,
    };

    // Only add pagination if explicitly requested
    if (isPaginated) {
      queryOptions.limit = limit;
      queryOptions.offset = offset;
    }

    const { rows: tickets, count } = await Ticket.findAndCountAll(queryOptions);

    // Format response based on whether pagination was requested
    let responseData;
    if (isPaginated) {
      responseData = getPaginatedItems(
        { count, rows: tickets },
        page as string,
        limit!
      );
    } else {
      responseData = {
        tickets,
        totalCount: count,
      };
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("TICKETS_FETCHED_SUCCESSFULLY"),
      data: responseData,
    });
  } catch (error) {
    console.error("Error fetching tickets:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("TICKETS_FETCH_FAILED"),
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * @description Get single ticket with messages and history
 * @route GET /api/v1/private/tickets/:id
 * @access Private
 */
const getTicketById = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { include_private = "false" } = req.query;

    const userId = (req as any).user?.id;
    const userOrganizationId = (req as any).user?.organization_id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS"),
      });
    }

    // Check if user has default access (super admin)
    const hasDefaultAccess = await isDefaultAccess(userId);

    // Build where clause with organization filter
    const whereClause: any = { id };

    // Add organization filter directly to query for optimization
    if (!hasDefaultAccess) {
      whereClause.organization_id = userOrganizationId;
    }

    const ticket = await Ticket.findOne({
      where: whereClause,
      include: [
        {
          model: TicketMessage,
          as: "messages",
          where: include_private === "true" ? {} : { is_private: false },
          required: false,
          order: [["created_at", "ASC"]],
        },
        {
          model: TicketHistory,
          as: "history",
          order: [["created_at", "DESC"]],
        },
      ],
    });

    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND"),
      });
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("TICKET_FETCHED_SUCCESSFULLY"),
      data: ticket,
    });
  } catch (error) {
    console.error("Error fetching ticket:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("TICKET_FETCH_FAILED"),
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * @description Create a support ticket with PIN verification (authenticated users only)
 * @route POST /api/v1/support/tickets
 * @access Authenticated users with valid support PIN
 *
 * IMPORTANT: Support PIN is REQUIRED for ticket creation (first time)
 * This ensures only authorized users can create tickets for an organization
 */
const createTicket = async (req: Request, res: Response): Promise<any> => {
  try {
    const {
      organization_id,
      ticket_owner_user_id, // NEW: For registered users (enables avatar/profile features)
      ticket_owner_name,
      ticket_owner_email,
      ticket_owner_phone,
      ticket_title,
      ticket_description,
      ticket_type = IssueType.GENERAL_QUERY,
      ticket_priority = Priority.MEDIUM,
      support_pin, // REQUIRED PIN for verification on ticket creation
    } = req.body;

    const userId = (req as any).user?.id;
    const userOrganizationId = (req as any).user?.organization_id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS"),
      });
    }

    // Check if user has default access (super admin)
    const hasDefaultAccess = await isDefaultAccess(userId);

    // Determine effective organization ID following recipe microservice pattern
    let ticketOrgId;
    if (hasDefaultAccess) {
      // Admin can specify organization_id in body or default to null for system-wide
      ticketOrgId = organization_id || null;
    } else {
      // Regular users must use their organization_id
      ticketOrgId = organization_id || userOrganizationId;
      if (!ticketOrgId) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: res.__("ORGANIZATION_ID_REQUIRED"),
        });
      }
    }

    // ========================================
    // SUPPORT PIN VALIDATION (REQUIRED FOR TICKET CREATION)
    // ========================================
    // This validation ensures only authorized users can create tickets
    // PIN is fetched from Keycloak organization attributes (same as auth microservice)

    if (!support_pin) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("SUPPORT_PIN_REQUIRED"),
        error: "Support PIN is mandatory for ticket creation",
      });
    }

    // Get auth token from request headers for Keycloak authentication
    const authToken = req.headers.authorization?.replace("Bearer ", "");

    // Validate PIN against organization data from Keycloak (following auth microservice pattern)
    const pinValidation = await organizationHelper.validateSupportPin(
      ticketOrgId,
      support_pin,
      authToken
    );

    if (!pinValidation.isValid) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("INVALID_SUPPORT_PIN"),
        error: pinValidation.error,
      });
    }

    // Handle file uploads
    const files = (req as any).files?.ticketFiles || [];
    const attachmentIds: number[] = [];

    if (files && files.length > 0) {
      for (const file of files) {
        const fileMetadata = uploadService.getFileMetadata(
          file,
          ticketOrgId,
          userId
        );
        if (fileMetadata) {
          const item = await Item.create(fileMetadata as any);
          attachmentIds.push(item.id);
        }
      }
    }

    // Create ticket
    const ticket = await Ticket.create({
      organization_id: ticketOrgId,
      ticket_owner_user_id, // Store user ID for future avatar/profile features
      ticket_owner_name,
      ticket_owner_email,
      ticket_owner_phone,
      ticket_title,
      ticket_description,
      ticket_module: null, // Will be set by admin later
      ticket_type,
      ticket_priority,
      ticket_status: TicketStatus.OPEN,
      created_by: userId,
    });

    // Create ticket attachments using proper TicketAttachment model
    if (attachmentIds.length > 0) {
      for (const itemId of attachmentIds) {
        await TicketAttachment.create({
          ticket_id: ticket.id,
          item_id: itemId,
          attachment_type: "document", // Default type, can be enhanced later
          created_by: userId,
        });
      }
    }

    // Generate ticket number
    ticket.ticket_number = await ticket.generateTicketNumber();
    await ticket.save();

    // Create initial history record
    await TicketHistory.create({
      ticket_id: ticket.id,
      action_type: "CREATED",
      new_status: TicketStatus.OPEN,
      change_note: "Ticket created by agent/admin",
      created_by: userId,
    });

    // Send email notification for ticket creation (using RabbitMQ)
    try {
      await notificationService.sendSupportTicketNotification(
        ticket.ticket_owner_email,
        "TICKET_CREATED",
        {
          ticketNumber: ticket.ticket_number,
          submitter_name: ticket.ticket_owner_name,
          subject: ticket.ticket_title,
          organization_id: ticket.organization_id,
          priority: ticket.ticket_priority,
          module_type: ticket.ticket_module,
        }
      );
    } catch (emailError) {
      console.error("Failed to send ticket creation notification:", emailError);
      // Don't fail the ticket creation if email fails
    }

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: res.__("TICKET_CREATED_SUCCESSFULLY"),
      data: ticket,
    });
  } catch (error) {
    console.error("Error creating ticket:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("TICKET_CREATION_FAILED"),
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * @description Assign ticket to agent with enhanced validation and AI suggestions
 * @route PUT /api/v1/private/tickets/:id/assign
 * @access Private
 *
 * IMPORTANT: Support PIN is NOT required for ticket assignment
 * Assignment is an internal operation performed by authenticated staff
 */
const assignTicket = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { assigned_to_user_id, change_note, validate_user = true } = req.body;
    const assigned_by = (req as any).user?.id;
    const userOrganizationId = (req as any).user?.organization_id;

    if (!assigned_by) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS"),
      });
    }

    // Check if user has default access (super admin)
    const hasDefaultAccess = await isDefaultAccess(assigned_by);

    if (!assigned_to_user_id) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("ASSIGNED_USER_ID_REQUIRED"),
      });
    }

    // Build where clause with organization filter for optimization
    const whereClause: any = { id };

    // Add organization filter directly to query
    if (!hasDefaultAccess) {
      whereClause.organization_id = userOrganizationId;
    }

    // Find ticket with organization filter
    const ticket = await Ticket.findOne({ where: whereClause });
    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND"),
      });
    }

    // Validate assignee if requested
    if (validate_user) {
      const assigneeDetails = await getUser(assigned_to_user_id);
      if (!assigneeDetails) {
        return res.status(StatusCodes.NOT_FOUND).json({
          status: false,
          message: res.__("ASSIGNEE_NOT_FOUND"),
        });
      }

      // Check if assignee is in the same organization (skip for super admins)
      if (
        !hasDefaultAccess &&
        assigneeDetails.organization_id !== userOrganizationId
      ) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: res.__("ASSIGNEE_ORGANIZATION_MISMATCH"),
        });
      }

      // Check if assignee is active
      if (assigneeDetails.user_status !== "active") {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: res.__("ASSIGNEE_NOT_ACTIVE"),
        });
      }

      // Check if assignee has appropriate roles
      const assigneeRoles = assigneeDetails.roles || "";
      const hasValidRole = ["admin", "agent", "support", "manager"].some(
        (role) => assigneeRoles.toLowerCase().includes(role)
      );

      if (!hasValidRole) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: res.__("ASSIGNEE_INSUFFICIENT_PERMISSIONS"),
        });
      }
    }

    const previousStatus = ticket.ticket_status;
    const previousAssignee = ticket.assigned_to_user_id;

    // Update ticket
    await ticket.update({
      assigned_to_user_id,
      assigned_by,
      assigned_at: new Date(),
      ticket_status: TicketStatus.ASSIGNED,
      updated_by: assigned_by,
    });

    // Create history record
    const historyNote =
      change_note ||
      (previousAssignee
        ? `Ticket reassigned from user ${previousAssignee} to user ${assigned_to_user_id}`
        : `Ticket assigned to user ${assigned_to_user_id}`);

    await TicketHistory.create({
      ticket_id: ticket.id,
      action_type: previousAssignee ? "REASSIGNED" : "ASSIGNED",
      previous_status: previousStatus,
      new_status: TicketStatus.ASSIGNED,
      change_note: historyNote,
      created_by: assigned_by,
    });

    // Send email notification for ticket assignment (using RabbitMQ)
    try {
      // Get user details for notification
      const assignedToUser = await getUser(assigned_to_user_id);
      const assignedByUser = await getUser(assigned_by);

      await notificationService.sendSupportTicketNotification(
        ticket.ticket_owner_email,
        "TICKET_ASSIGNED",
        {
          ticketNumber: ticket.ticket_number,
          submitter_name: ticket.ticket_owner_name,
          subject: ticket.ticket_title,
          assigned_to_name: assignedToUser?.user_full_name || "Unknown User",
          assigned_by_name: assignedByUser?.user_full_name || "Unknown User",
          assigned_to_email: assignedToUser?.user_email,
          is_reassignment: !!previousAssignee,
        }
      );

      // Also notify the assigned user
      if (assignedToUser?.user_email) {
        await notificationService.sendSupportTicketNotification(
          assignedToUser.user_email,
          "TICKET_ASSIGNED_TO_YOU",
          {
            ticketNumber: ticket.ticket_number,
            submitter_name: ticket.ticket_owner_name,
            subject: ticket.ticket_title,
            assigned_by_name: assignedByUser?.user_full_name || "Unknown User",
            priority: ticket.ticket_priority,
            description: ticket.ticket_description,
          }
        );
      }
    } catch (emailError) {
      console.error(
        "Failed to send ticket assignment notification:",
        emailError
      );
      // Don't fail the assignment if email fails
    }

    // Get assignee details for response
    const assigneeDetails = await getUser(assigned_to_user_id);

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("TICKET_ASSIGNED_SUCCESSFULLY"),
      data: {
        ticket_id: ticket.id,
        ticket_number: ticket.ticket_number,
        assigned_to: {
          user_id: assigned_to_user_id,
          name: assigneeDetails?.name || "Unknown User",
        },
        assigned_at: new Date(),
        is_reassignment: !!previousAssignee,
      },
    });
  } catch (error) {
    console.error("Error assigning ticket:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("TICKET_ASSIGNMENT_FAILED"),
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * @description Update ticket status
 * @route PUT /api/v1/private/tickets/:id/status
 * @access Private
 *
 * IMPORTANT: Support PIN is NOT required for status updates
 * Status updates are internal operations that don't require PIN validation
 */
const updateTicketStatus = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const { id } = req.params;
    const { status, change_note, resolution_note } = req.body;
    const updated_by = (req as any).user?.id;

    if (!updated_by) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS"),
      });
    }

    const ticket = await Ticket.findByPk(id);
    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND"),
      });
    }

    const previousStatus = ticket.ticket_status;
    const updateData: any = {
      ticket_status: status,
      updated_by,
    };

    // Set resolution fields if resolving
    if (status === TicketStatus.RESOLVED) {
      updateData.resolved_at = new Date();
      updateData.resolved_by = updated_by;
      if (resolution_note) {
        updateData.resolution_note = resolution_note;
      }
    }

    await ticket.update(updateData);

    // Create history record
    await TicketHistory.create({
      ticket_id: ticket.id,
      action_type: "STATUS_CHANGED",
      previous_status: previousStatus,
      new_status: status,
      change_note:
        change_note || `Status changed from ${previousStatus} to ${status}`,
      created_by: updated_by,
    });

    // Send email notification for status update (using RabbitMQ)
    try {
      const updatedByUser = await getUser(updated_by);

      if (status === TicketStatus.RESOLVED) {
        await notificationService.sendSupportTicketNotification(
          ticket.submitter_email,
          "TICKET_RESOLVED",
          {
            ticketNumber: ticket.ticket_number,
            submitter_name: ticket.submitter_name,
            subject: ticket.subject,
            resolved_by_name: updatedByUser?.name || "Unknown User",
            resolution_note: resolution_note,
          }
        );
      } else {
        await notificationService.sendSupportTicketNotification(
          ticket.submitter_email,
          "TICKET_STATUS_UPDATED",
          {
            ticketNumber: ticket.ticket_number,
            submitter_name: ticket.submitter_name,
            subject: ticket.subject,
            previous_status: previousStatus,
            new_status: status,
            updated_by_name: updatedByUser?.name || "Unknown User",
            resolution_note: resolution_note,
          }
        );
      }
    } catch (emailError) {
      console.error(
        "Failed to send ticket status update notification:",
        emailError
      );
      // Don't fail the status update if email fails
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("TICKET_STATUS_UPDATED"),
      data: {
        ticket_id: ticket.id,
        ticket_number: ticket.ticket_number,
        previous_status: previousStatus,
        new_status: status,
        updated_at: new Date(),
      },
    });
  } catch (error) {
    console.error("Error updating ticket status:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("TICKET_STATUS_UPDATE_FAILED"),
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * @description Update ticket information (Limited to essential fields only)
 * @route PUT /api/v1/private/tickets/:id
 * @access Private
 *
 * IMPORTANT: Support PIN is NOT required for ticket updates
 * This function provides limited update capabilities - for ongoing interaction use comments
 * Recommended: Use addTicketComment for most user interactions instead of full editing
 */
const updateTicket = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { priority } = req.body; // Only allow priority updates for now
    const updated_by = (req as any).user?.id;

    if (!updated_by) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS"),
      });
    }

    const ticket = await Ticket.findByPk(id);
    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND"),
      });
    }

    // Limited update - only priority changes allowed
    // For other changes, users should use the comment system
    const updateData: any = { updated_by };
    let changeNote = "Ticket updated";

    if (priority && priority !== ticket.priority) {
      updateData.priority = priority;
      changeNote = `Priority updated from ${ticket.priority} to ${priority}`;
    }

    // Only update if there are actual changes
    if (Object.keys(updateData).length > 1) {
      await ticket.update(updateData);

      // Create history record
      await TicketHistory.create({
        ticket_id: ticket.id,
        action_type: "UPDATED",
        change_note: changeNote,
        created_by: updated_by,
      });
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("TICKET_UPDATED_SUCCESSFULLY"),
      data: {
        ticket_id: ticket.id,
        ticket_number: ticket.ticket_number,
        updated_fields: Object.keys(updateData).filter(
          (key) => key !== "updated_by"
        ),
        updated_at: new Date(),
      },
    });
  } catch (error) {
    console.error("Error updating ticket:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("TICKET_UPDATE_FAILED"),
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * @description Update ticket module type (Admin only)
 * @route PUT /api/v1/private/tickets/:id/module
 * @access Private (Admin only)
 */
const updateTicketModule = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const { id } = req.params;
    const { module_type, change_note } = req.body;
    const updated_by = (req as any).user?.id;

    // Check if user has admin access (following recipe pattern)
    const hasDefaultAccess = await isDefaultAccess(updated_by);

    if (!hasDefaultAccess) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: res.__("ACCESS_DENIED"),
      });
    }

    // Find ticket
    const ticket = await Ticket.findByPk(id);
    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND"),
      });
    }

    const previousModuleType = ticket.module_type;

    // Update module type
    await ticket.update({
      module_type,
      updated_by,
    });

    // Create history record
    await TicketHistory.create({
      ticket_id: ticket.id,
      action_type: "MODULE_TYPE_UPDATED",
      previous_status: ticket.ticket_status,
      new_status: ticket.ticket_status,
      change_note:
        change_note ||
        `Module type updated from ${previousModuleType || "None"} to ${module_type}`,
      created_by: updated_by,
    });

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("TICKET_MODULE_UPDATED"),
      data: {
        ticket_id: ticket.id,
        ticket_number: ticket.ticket_number,
        previous_module: previousModuleType,
        new_module: module_type,
        updated_at: new Date(),
      },
    });
  } catch (error) {
    console.error("Error updating ticket module:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("TICKET_MODULE_UPDATE_FAILED"),
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * @description Add comment/reply to ticket
 * @route POST /api/v1/private/tickets/:id/comments
 * @access Private
 *
 * IMPORTANT: Support PIN is NOT required for adding comments
 * Comments are ongoing interactions that don't require PIN validation
 */
const addTicketComment = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { message_text, is_private = false } = req.body;
    const created_by = (req as any).user?.id;
    const user = (req as any).user;

    if (!created_by) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS"),
      });
    }

    // Check if user has default access (super admin)
    const hasDefaultAccess = await isDefaultAccess(created_by);

    // Build where clause with organization filter for optimization
    const whereClause: any = { id };

    // Add organization filter directly to query
    if (!hasDefaultAccess) {
      whereClause.organization_id = user?.organization_id;
    }

    // Find ticket with organization filter
    const ticket = await Ticket.findOne({ where: whereClause });
    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND"),
      });
    }

    // Determine message type based on user role and privacy
    let messageType: MessageType;
    if (is_private && (user?.isAdmin || user?.isAgent)) {
      messageType = MessageType.INTERNAL_NOTE; // Internal comment - only visible to agents/admins
    } else if (user?.isAdmin || user?.isAgent) {
      messageType = MessageType.AGENT; // Agent/Admin public comment
    } else {
      messageType = MessageType.USER; // User comment (always public)
    }

    // Handle file uploads
    const files = (req as any).files?.commentFiles || [];
    let attachmentId = null;

    if (files && files.length > 0) {
      const file = files[0]; // Take first file only for comments
      const fileMetadata = uploadService.getFileMetadata(
        file,
        ticket.organization_id,
        created_by
      );
      if (fileMetadata) {
        const item = await Item.create(fileMetadata as any);
        attachmentId = item.id;
      }
    }

    // Create comment/message
    const comment = await TicketMessage.create({
      ticket_id: parseInt(id),
      message_text,
      message_type: messageType,
      is_private: is_private && (user?.isAdmin || user?.isAgent), // Only agents/admins can create private comments
      attachment_id: attachmentId,
      created_by,
    });

    // Create history record
    const historyNote = is_private
      ? "Internal comment added"
      : `${messageType.toLowerCase()} comment added`;

    await TicketHistory.create({
      ticket_id: ticket.id,
      action_type: "COMMENT_ADDED",
      change_note: historyNote,
      created_by,
    });

    // Send email notification for public comments (using RabbitMQ)
    if (!is_private) {
      try {
        await notificationService.sendSupportTicketNotification(
          ticket.submitter_email,
          "TICKET_MESSAGE_ADDED",
          {
            ticketNumber: ticket.ticket_number,
            submitter_name: ticket.submitter_name,
            subject: ticket.subject,
            previous_status: ticket.ticket_status,
            new_status: ticket.ticket_status,
            resolution_note: `New comment added by ${messageType.toLowerCase()}`,
          }
        );
      } catch (emailError) {
        console.error("Failed to send comment notification:", emailError);
        // Don't fail the comment creation if email fails
      }
    }

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: res.__("COMMENT_ADDED_SUCCESSFULLY"),
      data: {
        comment_id: comment.id,
        ticket_id: ticket.id,
        message_type: messageType,
        is_private: comment.is_private,
        created_at: comment.created_at,
      },
    });
  } catch (error) {
    console.error("Error adding comment:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("COMMENT_ADD_FAILED"),
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * @description Get ticket comments with visibility filtering
 * @route GET /api/v1/private/tickets/:id/comments
 * @access Private
 */
const getTicketComments = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { include_private = "false" } = req.query;
    const user = (req as any).user;

    if (!user?.id) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS"),
      });
    }

    // Check if user has default access (super admin)
    const hasDefaultAccess = await isDefaultAccess(user.id);

    // Build where clause with organization filter for optimization
    const ticketWhereClause: any = { id };

    // Add organization filter directly to query
    if (!hasDefaultAccess) {
      ticketWhereClause.organization_id = user?.organization_id;
    }

    // Find ticket with organization filter
    const ticket = await Ticket.findOne({ where: ticketWhereClause });
    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND"),
      });
    }

    // Build where clause for comment visibility
    const whereClause: any = { ticket_id: id };

    // Only agents/admins can see private comments
    if (include_private === "true" && (user?.isAdmin || user?.isAgent)) {
      // Include all comments (public and private)
    } else {
      // Only public comments for regular users
      whereClause.is_private = false;
    }

    const comments = await TicketMessage.findAll({
      where: whereClause,
      include: [
        {
          model: Item,
          as: "attachment",
          required: false,
        },
      ],
      order: [["created_at", "ASC"]],
    });

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("COMMENTS_FETCHED_SUCCESSFULLY"),
      data: comments,
    });
  } catch (error) {
    console.error("Error fetching comments:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("COMMENTS_FETCH_FAILED"),
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * @description Get conversation for a ticket (API-based, replaces websocket)
 * @route GET /api/v1/private/tickets/:id/conversation
 * @access Private
 */
const getConversation = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { page, size, include_private = "false" } = req.query;
    const user = (req as any).user;

    if (!user?.id) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS"),
      });
    }

    // Check if user has default access (super admin)
    const hasDefaultAccess = await isDefaultAccess(user.id);

    // Build where clause with organization filter for optimization
    const ticketWhereClause: any = { id };

    // Add organization filter directly to query
    if (!hasDefaultAccess) {
      ticketWhereClause.organization_id = user?.organization_id;
    }

    // Find ticket with organization filter
    const ticket = await Ticket.findOne({ where: ticketWhereClause });
    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND"),
      });
    }

    // Get pagination parameters - only apply if explicitly requested
    let limit: number | undefined;
    let offset: number | undefined;
    let isPaginated = false;

    if (page || size) {
      const pagination = getPagination(page as string, size as string);
      limit = pagination.limit;
      offset = pagination.offset;
      isPaginated = true;
    }

    // Build where clause for message visibility
    const whereClause: any = { ticket_id: id };

    // Only agents/admins can see private messages
    if (include_private === "true" && (user?.isAdmin || user?.isAgent)) {
      // Include all messages (public and private)
    } else {
      // Only public messages for regular users
      whereClause.is_private = false;
    }

    // Build query options
    const queryOptions: any = {
      where: whereClause,
      include: [
        {
          model: Item,
          as: "attachment",
          required: false,
        },
      ],
      order: [["created_at", "ASC"]],
    };

    // Only add pagination if explicitly requested
    if (isPaginated) {
      queryOptions.limit = limit;
      queryOptions.offset = offset;
    }

    const { rows: messages, count } =
      await TicketMessage.findAndCountAll(queryOptions);

    // Format response based on whether pagination was requested
    let conversationData;
    if (isPaginated) {
      conversationData = getPaginatedItems(
        { count, rows: messages },
        page as string,
        limit!
      );
    } else {
      conversationData = {
        messages,
        totalCount: count,
      };
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("MESSAGES_FETCHED_SUCCESSFULLY"),
      data: {
        ticket: {
          id: ticket.id,
          ticket_number: ticket.ticket_number,
          subject: ticket.subject,
          status: ticket.ticket_status,
          priority: ticket.priority,
          created_at: ticket.created_at,
          submitter_name: ticket.submitter_name,
        },
        conversation: conversationData,
      },
    });
  } catch (error) {
    console.error("Error fetching conversation:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("ERROR_FETCHING_MESSAGES"),
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * @description Send message in conversation (API-based, replaces websocket)
 * @route POST /api/v1/private/tickets/:id/conversation
 * @access Private
 */
const sendConversationMessage = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const { id } = req.params;
    const { message_text, is_private = false } = req.body;
    const user = (req as any).user;

    if (!user?.id) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS"),
      });
    }

    // Check if user has default access (super admin)
    const hasDefaultAccess = await isDefaultAccess(user.id);

    // Build where clause with organization filter for optimization
    const ticketWhereClause: any = { id };

    // Add organization filter directly to query
    if (!hasDefaultAccess) {
      ticketWhereClause.organization_id = user?.organization_id;
    }

    // Find ticket with organization filter
    const ticket = await Ticket.findOne({ where: ticketWhereClause });
    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND"),
      });
    }

    // Determine message type based on user role
    let messageType: MessageType;
    if (is_private && (user?.isAdmin || user?.isAgent)) {
      messageType = MessageType.INTERNAL_NOTE;
    } else if (user?.isAdmin || user?.isAgent) {
      messageType = MessageType.AGENT;
    } else {
      messageType = MessageType.USER;
    }

    // Handle file uploads
    const files = (req as any).files?.messageFiles || [];
    let attachment_id = null;

    if (files && files.length > 0) {
      const file = files[0];
      const fileMetadata = uploadService.getFileMetadata(
        file,
        ticket.organization_id,
        user.id
      );
      if (fileMetadata) {
        const item = await Item.create(fileMetadata as any);
        attachment_id = item.id;
      }
    }

    // Create message
    const message = await TicketMessage.create({
      ticket_id: parseInt(id),
      message_text,
      message_type: messageType,
      is_private: is_private && (user?.isAdmin || user?.isAgent),
      attachment_id,
      created_by: user.id,
    });

    // Update ticket's last activity
    await ticket.update({
      last_activity_at: new Date(),
      updated_by: user.id,
    });

    // Update first_response_at if this is the first agent response
    if (messageType === MessageType.AGENT && !ticket.first_response_at) {
      await ticket.update({
        first_response_at: new Date(),
      });
    }

    // Auto-update ticket status if needed
    if (
      messageType === MessageType.AGENT &&
      ticket.ticket_status === TicketStatus.OPEN
    ) {
      await ticket.update({
        ticket_status: TicketStatus.IN_PROGRESS,
      });

      await TicketHistory.create({
        ticket_id: ticket.id,
        action_type: "STATUS_CHANGED",
        previous_status: TicketStatus.OPEN,
        new_status: TicketStatus.IN_PROGRESS,
        change_note: "Status updated to In Progress after agent response",
        created_by: user.id,
      });
    }

    // Create history record
    const historyNote = is_private
      ? "Internal message added"
      : `${messageType.toLowerCase()} message added`;

    await TicketHistory.create({
      ticket_id: ticket.id,
      action_type: "MESSAGE_ADDED",
      change_note: historyNote,
      created_by: user.id,
    });

    // Send email notification for public messages
    if (!is_private) {
      try {
        await notificationService.sendSupportTicketNotification(
          ticket.submitter_email,
          "TICKET_MESSAGE_ADDED",
          {
            ticketNumber: ticket.ticket_number,
            submitter_name: ticket.submitter_name,
            subject: ticket.subject,
            message_text: message_text,
            message_from: user?.name || "Support Agent",
            is_agent_response: messageType === MessageType.AGENT,
          }
        );
      } catch (emailError) {
        console.error("Failed to send message notification:", emailError);
      }
    }

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: res.__("MESSAGE_CREATED_SUCCESSFULLY"),
      data: {
        message_id: message.id,
        ticket_id: ticket.id,
        message_type: messageType,
        is_private: message.is_private,
        created_at: message.created_at,
      },
    });
  } catch (error) {
    console.error("Error sending conversation message:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("ERROR_SENDING_MESSAGE"),
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * @description Get assignable users for ticket assignment (integrated user management)
 * @route GET /api/v1/private/support/assignable-users
 * @access Private (Admin/Agent only)
 */
const getAssignableUsersForTickets = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const {
      search,
      roleFilter,
      departmentId,
      branchId,
      page,
      limit,
      includeInactive = "false",
      organization_id,
    } = req.query;

    const user = (req as any).user;

    // Check if user has default access (super admin)
    const hasDefaultAccess = await isDefaultAccess(user?.id);

    // Determine effective organization ID following recipe microservice pattern
    let effectiveOrganizationId;
    if (hasDefaultAccess) {
      // Admin can specify organization_id in query, or get all data if not specified
      if (organization_id !== undefined) {
        effectiveOrganizationId =
          organization_id === "null" || organization_id === ""
            ? null
            : organization_id;
      }
      // If no organization specified, return undefined to get all data
      effectiveOrganizationId = effectiveOrganizationId || undefined;
    } else {
      // Regular users are restricted to their organization
      effectiveOrganizationId = user?.organization_id;
      if (!effectiveOrganizationId) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: res.__("ORGANIZATION_ID_REQUIRED"),
        });
      }
    }

    const options = {
      search: search as string,
      roleFilter: roleFilter as string,
      departmentId: departmentId ? parseInt(departmentId as string) : undefined,
      branchId: branchId ? parseInt(branchId as string) : undefined,
      page: page ? parseInt(page as string) : undefined,
      limit: limit ? parseInt(limit as string) : undefined,
      includeInactive: includeInactive === "true",
    };

    const result = await getAssignableUsers(effectiveOrganizationId, options);

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("USERS_FETCHED_SUCCESSFULLY"),
      data: result,
    });
  } catch (error) {
    console.error("Error fetching assignable users:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("ERROR_FETCHING_USERS"),
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * @description Submit rating and feedback for a ticket
 * @route POST /api/v1/private/support/tickets/:id/feedback
 * @access Private
 */
const submitTicketFeedback = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const { id } = req.params;
    const { rating, review_comment } = req.body;
    const user = (req as any).user;

    if (!user?.id) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS"),
      });
    }

    // Check if user has default access (super admin)
    const hasDefaultAccess = await isDefaultAccess(user.id);

    // Build where clause with organization filter for optimization
    const ticketWhereClause: any = { id };

    // Add organization filter directly to query
    if (!hasDefaultAccess) {
      ticketWhereClause.organization_id = user?.organization_id;
    }

    // Find ticket with organization filter
    const ticket = await Ticket.findOne({ where: ticketWhereClause });
    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND"),
      });
    }

    // Check if ticket is resolved or closed
    if (
      ![TicketStatus.RESOLVED, TicketStatus.CLOSED].includes(
        ticket.ticket_status as TicketStatus
      )
    ) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("FEEDBACK_ONLY_FOR_RESOLVED_TICKETS"),
      });
    }

    // Update ticket with feedback
    await ticket.update({
      rating,
      review_comment: review_comment || null,
      reviewed_at: new Date(),
      updated_by: user.id,
    });

    // Create history record
    await TicketHistory.create({
      ticket_id: parseInt(id),
      action_type: "UPDATED",
      field_changed: "rating_feedback",
      new_value: `Rating: ${rating}${review_comment ? `, Feedback: ${review_comment}` : ""}`,
      change_note: "Customer feedback submitted",
      created_by: user.id,
      ip_address: req.ip,
      user_agent: req.get("User-Agent"),
    });

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("FEEDBACK_SUBMITTED_SUCCESSFULLY"),
      data: {
        ticket_id: ticket.id,
        rating,
        review_comment,
        reviewed_at: new Date(),
      },
    });
  } catch (error) {
    console.error("Error submitting feedback:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("FEEDBACK_SUBMISSION_FAILED"),
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * @description Get feedback statistics for organization
 * @route GET /api/v1/private/support/feedback/stats
 * @access Private
 */
const getFeedbackStats = async (req: Request, res: Response): Promise<any> => {
  try {
    const { organization_id, date_from, date_to } = req.query;
    const user = (req as any).user;

    if (!user?.id) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS"),
      });
    }

    // Check if user has default access (super admin)
    const hasDefaultAccess = await isDefaultAccess(user.id);

    // Determine effective organization ID following recipe microservice pattern
    let effectiveOrganizationId;
    if (hasDefaultAccess) {
      effectiveOrganizationId = organization_id || undefined;
    } else {
      effectiveOrganizationId = user?.organization_id;
    }

    const whereClause: any = {
      rating: { [Op.not]: null },
    };

    if (effectiveOrganizationId) {
      whereClause.organization_id = effectiveOrganizationId;
    }

    if (date_from) {
      whereClause.reviewed_at = { [Op.gte]: new Date(date_from as string) };
    }

    if (date_to) {
      whereClause.reviewed_at = {
        ...whereClause.reviewed_at,
        [Op.lte]: new Date(date_to as string),
      };
    }

    // Get feedback statistics
    const stats = await Ticket.findOne({
      where: whereClause,
      attributes: [
        [
          Ticket.sequelize!.fn("AVG", Ticket.sequelize!.col("rating")),
          "avg_rating",
        ],
        [
          Ticket.sequelize!.fn("COUNT", Ticket.sequelize!.col("rating")),
          "total_ratings",
        ],
        [
          Ticket.sequelize!.fn(
            "SUM",
            Ticket.sequelize!.literal("CASE WHEN rating >= 4 THEN 1 ELSE 0 END")
          ),
          "positive_ratings",
        ],
        [
          Ticket.sequelize!.fn(
            "SUM",
            Ticket.sequelize!.literal("CASE WHEN rating <= 2 THEN 1 ELSE 0 END")
          ),
          "negative_ratings",
        ],
      ],
      raw: true,
    });

    // Get rating distribution
    const ratingDistribution = await Ticket.findAll({
      where: whereClause,
      attributes: [
        "rating",
        [
          Ticket.sequelize!.fn("COUNT", Ticket.sequelize!.col("rating")),
          "count",
        ],
      ],
      group: ["rating"],
      order: [["rating", "ASC"]],
      raw: true,
    });

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("FEEDBACK_STATS_FETCHED_SUCCESSFULLY"),
      data: {
        summary: {
          average_rating: parseFloat((stats as any)?.avg_rating || "0").toFixed(
            2
          ),
          total_ratings: parseInt((stats as any)?.total_ratings || "0"),
          positive_ratings: parseInt((stats as any)?.positive_ratings || "0"),
          negative_ratings: parseInt((stats as any)?.negative_ratings || "0"),
          satisfaction_rate:
            stats && (stats as any).total_ratings > 0
              ? Math.round(
                  ((stats as any).positive_ratings /
                    (stats as any).total_ratings) *
                    100
                )
              : 0,
        },
        distribution: ratingDistribution,
        filters: {
          organization_id: effectiveOrganizationId,
          date_from: date_from || null,
          date_to: date_to || null,
        },
      },
    });
  } catch (error) {
    console.error("Error fetching feedback stats:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("FEEDBACK_STATS_FETCH_FAILED"),
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * @description Handle export functionality for tickets
 * @param queryParams - Request query parameters
 * @param tickets - Fetched tickets data
 * @param count - Total count of tickets
 * @param downloadType - 'excel' or 'csv'
 * @param res - Response object
 * @param organizationId - Organization ID for context
 */
const handleTicketExport = async (
  queryParams: any,
  tickets: any[],
  count: number,
  downloadType: string,
  res: Response,
  organizationId?: string
): Promise<any> => {
  try {
    const {
      status,
      priority,
      assigned_to_user_id,
      search,
      sort_by,
      sort_order,
      unassigned,
    } = queryParams;

    // Build filter info for export metadata
    const filters = [];
    if (status) filters.push(`Status: ${status}`);
    if (priority) filters.push(`Priority: ${priority}`);
    if (assigned_to_user_id)
      filters.push(`Assigned to: ${assigned_to_user_id}`);
    if (unassigned === "true") filters.push("Unassigned tickets only");
    if (search) filters.push(`Search: ${search}`);
    if (sort_by) filters.push(`Sort by: ${sort_by} ${sort_order || "ASC"}`);
    if (organizationId) filters.push(`Organization: ${organizationId}`);

    const filterInfo =
      filters.length > 0 ? filters.join(", ") : "No filters applied";

    if (downloadType === "excel") {
      return await exportTicketsToExcel(
        tickets,
        count,
        filterInfo,
        res,
        organizationId
      );
    } else if (downloadType === "csv") {
      return await exportTicketsToCSV(tickets, count, filterInfo, res);
    }
  } catch (error: unknown) {
    const customError = error as Error;
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("ERROR_EXPORTING_TICKETS"),
      error: customError.message,
    });
  }
};

/**
 * @description Export tickets to Excel format
 */
const exportTicketsToExcel = async (
  tickets: any[],
  count: number,
  filterInfo: string,
  res: Response,
  organizationId?: string
): Promise<any> => {
  // Check for large datasets and warn user
  if (count > 10000) {
    return res.status(StatusCodes.BAD_REQUEST).json({
      status: false,
      message:
        "Dataset too large for export. Please apply filters to reduce the number of records below 10,000.",
      current_count: count,
      max_allowed: 10000,
    });
  }

  // Create Excel workbook
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet("Support Tickets Export");

  // Add metadata header
  worksheet.addRow(["Support Tickets Export Report"]);
  worksheet.addRow(["Generated on:", new Date().toISOString()]);
  worksheet.addRow(["Organization:", organizationId || "All Organizations"]);
  worksheet.addRow(["Filters Applied:", filterInfo]);
  worksheet.addRow(["Total Records:", count]);
  worksheet.addRow([]); // Empty row

  // Define headers
  const headers = [
    "Ticket ID",
    "Ticket Number",
    "Subject",
    "Description",
    "Status",
    "Priority",
    "Module",
    "Issue Type",
    "Submitter Name",
    "Submitter Email",
    "Submitter Phone",
    "Assigned To",
    "Organization ID",
    "Created At",
    "Updated At",
    "Resolved At",
    "Last Activity",
  ];

  // Add headers row
  const headerRow = worksheet.addRow(headers);
  headerRow.font = { bold: true };
  headerRow.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFE0E0E0" },
  };

  // Add data rows
  for (const ticket of tickets) {
    const row = [
      ticket.id,
      ticket.ticket_number,
      ticket.ticket_title || ticket.subject,
      ticket.ticket_description || ticket.description,
      ticket.ticket_status,
      ticket.ticket_priority || ticket.priority,
      ticket.ticket_module || ticket.module_type,
      ticket.ticket_type || ticket.issue_type,
      ticket.ticket_owner_name || ticket.submitter_name,
      ticket.ticket_owner_email || ticket.submitter_email,
      ticket.ticket_owner_phone || ticket.submitter_phone,
      ticket.assigned_to_user_id || "Unassigned",
      ticket.organization_id,
      ticket.created_at,
      ticket.updated_at,
      ticket.resolved_at || "Not Resolved",
      ticket.last_activity_at || ticket.updated_at,
    ];

    worksheet.addRow(row);
  }

  // Auto-fit columns
  worksheet.columns.forEach((column: any) => {
    column.width = Math.max(column.width || 10, 15);
  });

  // Add header and footer for printing
  worksheet.headerFooter.oddHeader = `&C&"Arial,Bold"&14Support Tickets Export`;
  worksheet.headerFooter.oddFooter = `&L&"Arial"&10Generated on: ${new Date().toLocaleString()}&R&"Arial"&10Page &P of &N`;

  // Set response headers
  const filename = `support_tickets_export_${new Date().toISOString().split("T")[0]}.xlsx`;
  res.setHeader(
    "Content-Type",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  );
  res.setHeader("Content-Disposition", `attachment; filename=${filename}`);

  await workbook.xlsx.write(res);
  res.end();
};

/**
 * @description Export tickets to CSV format
 */
const exportTicketsToCSV = async (
  tickets: any[],
  count: number,
  filterInfo: string,
  res: Response
): Promise<any> => {
  // CSV Headers
  const csvHeaders = [
    "Ticket ID",
    "Ticket Number",
    "Subject",
    "Description",
    "Status",
    "Priority",
    "Module",
    "Issue Type",
    "Submitter Name",
    "Submitter Email",
    "Submitter Phone",
    "Assigned To",
    "Organization ID",
    "Created At",
    "Updated At",
    "Resolved At",
    "Last Activity",
  ];

  // Helper function to escape CSV values
  const escapeCSV = (value: any): string => {
    if (value === null || value === undefined) return "-";
    const str = String(value);
    if (str.includes(",") || str.includes('"') || str.includes("\n")) {
      return `"${str.replace(/"/g, '""')}"`;
    }
    return str;
  };

  // Build CSV content
  let csvContent = `# Export Filters Applied: ${filterInfo}\n`;
  csvContent += `# Export Date: ${new Date().toISOString()}\n`;
  csvContent += `# Total Records: ${count}\n`;
  csvContent += "\n";
  csvContent += csvHeaders.join(",") + "\n";

  // Add data rows
  for (const ticket of tickets) {
    const row = [
      ticket.id,
      ticket.ticket_number,
      ticket.ticket_title || ticket.subject,
      ticket.ticket_description || ticket.description,
      ticket.ticket_status,
      ticket.ticket_priority || ticket.priority,
      ticket.ticket_module || ticket.module_type,
      ticket.ticket_type || ticket.issue_type,
      ticket.ticket_owner_name || ticket.submitter_name,
      ticket.ticket_owner_email || ticket.submitter_email,
      ticket.ticket_owner_phone || ticket.submitter_phone,
      ticket.assigned_to_user_id || "Unassigned",
      ticket.organization_id,
      ticket.created_at,
      ticket.updated_at,
      ticket.resolved_at || "Not Resolved",
      ticket.last_activity_at || ticket.updated_at,
    ];

    csvContent += row.map(escapeCSV).join(",") + "\n";
  }

  // Set response headers
  const filename = `support_tickets_export_${new Date().toISOString().split("T")[0]}.csv`;
  res.setHeader("Content-Type", "text/csv");
  res.setHeader("Content-Disposition", `attachment; filename=${filename}`);

  res.send(csvContent);
};

// Default export for the controller
export default {
  getAllTickets,
  getTicketById,
  createTicket,
  assignTicket,
  updateTicketStatus,
  updateTicket,
  updateTicketModule,
  addTicketComment,
  getTicketComments,
  getConversation,
  sendConversationMessage,
  getAssignableUsersForTickets,
  submitTicketFeedback,
  getFeedbackStats,
};
