import { Request, Response, NextFunction } from "express";
import { StatusCodes } from "http-status-codes";

/**
 * Enhanced input validation utility for Customer Service module
 * Following the same patterns as the recipe module
 */
export class ValidationHelper {
  /**
   * Check validation results and return errors if any
   */
  static checkValidation(req: Request, res: Response, next: NextFunction) {
    // Since we're using celebrate for validation, this is a placeholder
    // The actual validation is handled by celebrate middleware
    next();
  }

  /**
   * Validate required string field
   */
  static validateRequiredString(
    value: any,
    fieldName: string,
    minLength: number = 1,
    maxLength: number = 255
  ): string | null {
    if (!value || typeof value !== "string") {
      return `${fieldName} is required and must be a string`;
    }

    const trimmedValue = value.trim();
    if (trimmedValue.length < minLength) {
      return `${fieldName} must be at least ${minLength} characters long`;
    }

    if (trimmedValue.length > maxLength) {
      return `${fieldName} cannot exceed ${maxLength} characters`;
    }

    return null;
  }

  /**
   * Validate optional string field
   */
  static validateOptionalString(
    value: any,
    fieldName: string,
    maxLength: number = 255
  ): string | null {
    if (value === null || value === undefined || value === "") {
      return null; // Optional field, no error
    }

    if (typeof value !== "string") {
      return `${fieldName} must be a string`;
    }

    if (value.length > maxLength) {
      return `${fieldName} cannot exceed ${maxLength} characters`;
    }

    return null;
  }

  /**
   * Validate email format
   */
  static validateEmail(value: any, fieldName: string): string | null {
    if (!value || typeof value !== "string") {
      return `${fieldName} is required and must be a valid email`;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
      return `${fieldName} must be a valid email address`;
    }

    return null;
  }

  /**
   * Validate phone number format
   */
  static validatePhone(
    value: any,
    fieldName: string,
    required: boolean = false
  ): string | null {
    if (!value || value === "") {
      return required ? `${fieldName} is required` : null;
    }

    if (typeof value !== "string") {
      return `${fieldName} must be a string`;
    }

    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    if (!phoneRegex.test(value)) {
      return `${fieldName} must be a valid phone number`;
    }

    return null;
  }

  /**
   * Validate number field
   */
  static validateNumber(
    value: any,
    fieldName: string,
    min?: number,
    max?: number
  ): string | null {
    if (value === null || value === undefined) {
      return null; // Allow null/undefined for optional fields
    }

    const numValue = Number(value);
    if (isNaN(numValue)) {
      return `${fieldName} must be a valid number`;
    }

    if (min !== undefined && numValue < min) {
      return `${fieldName} must be at least ${min}`;
    }

    if (max !== undefined && numValue > max) {
      return `${fieldName} cannot exceed ${max}`;
    }

    return null;
  }

  /**
   * Validate positive integer
   */
  static validatePositiveInteger(value: any, fieldName: string): string | null {
    if (value === null || value === undefined) {
      return null;
    }

    const numValue = Number(value);
    if (isNaN(numValue) || !Number.isInteger(numValue) || numValue <= 0) {
      return `${fieldName} must be a positive integer`;
    }

    return null;
  }

  /**
   * Validate UUID format
   */
  static validateUUID(value: any, fieldName: string): string | null {
    if (!value || typeof value !== "string") {
      return `${fieldName} is required and must be a valid UUID`;
    }

    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(value)) {
      return `${fieldName} must be a valid UUID`;
    }

    return null;
  }

  /**
   * Validate enum value
   */
  static validateEnum(
    value: any,
    fieldName: string,
    allowedValues: string[]
  ): string | null {
    if (!value) {
      return `${fieldName} is required`;
    }

    if (!allowedValues.includes(value)) {
      return `${fieldName} must be one of: ${allowedValues.join(", ")}`;
    }

    return null;
  }

  /**
   * Validate date format
   */
  static validateDate(value: any, fieldName: string): string | null {
    if (!value) {
      return null; // Allow null for optional dates
    }

    const date = new Date(value);
    if (isNaN(date.getTime())) {
      return `${fieldName} must be a valid date`;
    }

    return null;
  }

  /**
   * Validate date range
   */
  static validateDateRange(
    startDate: any,
    endDate: any,
    startFieldName: string = "start_date",
    endFieldName: string = "end_date"
  ): string | null {
    const startError = this.validateDate(startDate, startFieldName);
    if (startError) return startError;

    const endError = this.validateDate(endDate, endFieldName);
    if (endError) return endError;

    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);

      if (start > end) {
        return `${startFieldName} cannot be after ${endFieldName}`;
      }
    }

    return null;
  }

  /**
   * Validate array field
   */
  static validateArray(
    value: any,
    fieldName: string,
    minLength?: number,
    maxLength?: number
  ): string | null {
    if (!Array.isArray(value)) {
      return `${fieldName} must be an array`;
    }

    if (minLength !== undefined && value.length < minLength) {
      return `${fieldName} must contain at least ${minLength} items`;
    }

    if (maxLength !== undefined && value.length > maxLength) {
      return `${fieldName} cannot contain more than ${maxLength} items`;
    }

    return null;
  }

  /**
   * Validate file upload
   */
  static validateFileUpload(
    file: any,
    allowedTypes: string[],
    maxSize: number
  ): string | null {
    if (!file) {
      return "File is required";
    }

    if (!allowedTypes.includes(file.mimetype)) {
      return `File type not allowed. Allowed types: ${allowedTypes.join(", ")}`;
    }

    if (file.size > maxSize) {
      return `File size exceeds maximum limit of ${Math.round(maxSize / 1024 / 1024)}MB`;
    }

    return null;
  }

  /**
   * Sanitize input to prevent XSS
   */
  static sanitizeInput(input: string): string {
    if (typeof input !== "string") return "";

    return input
      .replace(/[<>]/g, "") // Remove < and >
      .replace(/javascript:/gi, "") // Remove javascript: protocol
      .replace(/on\w+=/gi, "") // Remove event handlers
      .trim();
  }

  /**
   * Validate ticket number format
   */
  static validateTicketNumber(value: any): string | null {
    if (!value || typeof value !== "string") {
      return "Ticket number is required";
    }

    const ticketNumberRegex = /^TKT-\d{8}-\d{4}$/;
    if (!ticketNumberRegex.test(value)) {
      return "Invalid ticket number format. Expected format: TKT-YYYYMMDD-NNNN";
    }

    return null;
  }

  /**
   * Validate priority level
   */
  static validatePriority(value: any): string | null {
    const allowedPriorities = [
      "none",
      "low",
      "medium",
      "high",
      "urgent",
      "emergency",
    ];
    return this.validateEnum(value, "priority", allowedPriorities);
  }

  /**
   * Validate ticket status
   */
  static validateTicketStatus(value: any): string | null {
    const allowedStatuses = [
      "open",
      "in_progress",
      "escalated",
      "qa_review",
      "under_review",
      "on_hold",
      "resolved",
      "closed",
      "invoiced",
      "assigned",
    ];
    return this.validateEnum(value, "status", allowedStatuses);
  }

  /**
   * Validate message visibility
   */
  static validateMessageVisibility(value: any): string | null {
    const allowedVisibilities = ["public", "private", "internal"];
    return this.validateEnum(value, "visibility", allowedVisibilities);
  }
}

/**
 * Common validation utilities for different entities
 */
export const CommonValidations = {
  /**
   * Validate ticket data
   */
  validateTicketData(data: any): string[] {
    const errors: string[] = [];

    const titleError = ValidationHelper.validateRequiredString(
      data.ticket_title,
      "ticket_title",
      5,
      255
    );
    if (titleError) errors.push(titleError);

    const descError = ValidationHelper.validateRequiredString(
      data.ticket_description,
      "ticket_description",
      10,
      5000
    );
    if (descError) errors.push(descError);

    const ownerNameError = ValidationHelper.validateRequiredString(
      data.ticket_owner_name,
      "ticket_owner_name",
      2,
      100
    );
    if (ownerNameError) errors.push(ownerNameError);

    const emailError = ValidationHelper.validateEmail(
      data.ticket_owner_email,
      "ticket_owner_email"
    );
    if (emailError) errors.push(emailError);

    const phoneError = ValidationHelper.validatePhone(
      data.ticket_owner_phone,
      "ticket_owner_phone",
      false
    );
    if (phoneError) errors.push(phoneError);

    const priorityError = ValidationHelper.validatePriority(
      data.ticket_priority
    );
    if (priorityError) errors.push(priorityError);

    return errors;
  },

  /**
   * Validate message data
   */
  validateMessageData(data: any): string[] {
    const errors: string[] = [];

    const contentError = ValidationHelper.validateRequiredString(
      data.message_content,
      "message_content",
      1,
      2000
    );
    if (contentError) errors.push(contentError);

    const ticketIdError = ValidationHelper.validateUUID(
      data.ticket_id,
      "ticket_id"
    );
    if (ticketIdError) errors.push(ticketIdError);

    const visibilityError = ValidationHelper.validateMessageVisibility(
      data.message_visibility
    );
    if (visibilityError) errors.push(visibilityError);

    if (data.parent_message_id) {
      const parentIdError = ValidationHelper.validateUUID(
        data.parent_message_id,
        "parent_message_id"
      );
      if (parentIdError) errors.push(parentIdError);
    }

    return errors;
  },

  /**
   * Validate escalation data
   */
  validateEscalationData(data: any): string[] {
    const errors: string[] = [];

    const reasonError = ValidationHelper.validateRequiredString(
      data.reason,
      "reason",
      10,
      500
    );
    if (reasonError) errors.push(reasonError);

    return errors;
  },

  /**
   * Validate assignment data
   */
  validateAssignmentData(data: any): string[] {
    const errors: string[] = [];

    const userIdError = ValidationHelper.validatePositiveInteger(
      data.assigned_to_user_id,
      "assigned_to_user_id"
    );
    if (userIdError) errors.push(userIdError);

    return errors;
  },
};

/**
 * Validation middleware factory
 */
export const createValidationMiddleware = (
  validationFunction: (data: any) => string[]
) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationFunction(req.body);

    if (errors.length > 0) {
      res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("VALIDATION_ERROR") || "Validation failed",
        errors: errors,
      });
      return;
    }

    next();
  };
};

export default ValidationHelper;
