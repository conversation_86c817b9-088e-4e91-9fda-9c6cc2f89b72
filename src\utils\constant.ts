// Following the same pattern as recipe module constants

export const ROLE_CONSTANT = Object.freeze({
  SUPER_ADMIN: "Super Admin",
  ADMIN: "Admin",
  DIRECTOR: "Director",
  HR: "HR",
  AREA_MANAGER: "Area Manager",
  ACCOUNTANT: "Accountant",
  <PERSON><PERSON>CH_MANAGER: "Branch Manager",
  <PERSON>SIG<PERSON>_BRANCH_MANAGER: "Assist. Branch Manager",
  HEAD_CHEF: "Head Chef",
  BAR_MANAGER: "Bar Manager",
  FOH: "FOH",
  BAR: "Bar",
  KITCHEN: "Kitchen",
  HOTEL_MANAGER: "Hotel Manager",
  ASSIGN_HOTEL_MANAGER: "Assist. Hotel Manager",
  RECEPTIONIST: "Receptionist",
  HEAD_HOUSEKEEPER: "Head Housekeeper",
  HOUSE_KEEPER: "House Keeper",
  SIGNATURE: "Signature",
});

export const ADMIN_SIDE_USER = [
  ROLE_CONSTANT.SUPER_ADMIN,
  ROLE_CONSTANT.ADMIN,
  R<PERSON><PERSON>_CONSTANT.DIRECTOR,
  ROLE_CONSTANT.ACCOUNTANT,
  ROLE_CONSTANT.HR,
  ROLE_CONSTANT.AREA_MANAGER,
  ROLE_CONSTANT.BRANCH_MANAGER,
  ROLE_CONSTANT.HOTEL_MANAGER,
  ROLE_CONSTANT.SIGNATURE,
];

export const NORMAL_USER = [
  ROLE_CONSTANT.SUPER_ADMIN,
  ROLE_CONSTANT.ADMIN,
  ROLE_CONSTANT.BRANCH_MANAGER,
  ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER,
  ROLE_CONSTANT.HEAD_CHEF,
  ROLE_CONSTANT.BAR_MANAGER,
  ROLE_CONSTANT.FOH,
  ROLE_CONSTANT.BAR,
  ROLE_CONSTANT.KITCHEN,
  ROLE_CONSTANT.HOTEL_MANAGER,
  ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER,
  ROLE_CONSTANT.RECEPTIONIST,
  ROLE_CONSTANT.HEAD_HOUSEKEEPER,
  ROLE_CONSTANT.HOUSE_KEEPER,
];

/**
 * Ticket status constants
 */
export const TICKET_STATUS = {
  OPEN: "open",
  IN_PROGRESS: "in_progress",
  ESCALATED: "escalated",
  QA_REVIEW: "qa_review",
  UNDER_REVIEW: "under_review",
  ON_HOLD: "on_hold",
  RESOLVED: "resolved",
  CLOSED: "closed",
  INVOICED: "invoiced",
  ASSIGNED: "assigned",
};

/**
 * Ticket priority constants
 *
 * NONE: Used for filtering/display purposes when no priority filter is applied
 * LOW: Minor issues, can be addressed in regular workflow
 * MEDIUM: Standard priority, typical business issues (default)
 * HIGH: Important issues requiring prompt attention
 * URGENT: Critical issues requiring immediate attention
 */
export const TICKET_PRIORITY = {
  NONE: "none", // Filter placeholder - not a database value
  LOW: "low",
  MEDIUM: "medium",
  HIGH: "high",
  URGENT: "urgent",
};

/**
 * Ticket module constants - Business system categorization
 *
 * HRMS: Human Resource Management System - Employee management, payroll, onboarding
 * PMS: Property Management System - Property operations, bookings, maintenance
 * RECIPE: Recipe Management System - Food recipes, ingredients, menu planning
 * OTHER: General/Miscellaneous - Issues not specific to above modules
 */
export const TICKET_MODULE = {
  HRMS: "hrms",
  PMS: "pms",
  RECIPE: "recipe",
  OTHER: "other",
};

/**
 * Ticket type constants
 */
export const TICKET_TYPE = {
  BUG: "bug",
  FEATURE_REQUEST: "feature_request",
  GENERAL_QUERY: "general_query",
  TECHNICAL: "technical",
  NON_TECHNICAL: "non_technical",
  EXPORT_HELP: "export_help",
  SUPPORT: "support",
};

/**
 * Message sender type constants
 */
export const MESSAGE_SENDER_TYPE = {
  USER: "user",
  AI: "ai",
  AGENT: "agent",
  SYSTEM: "system",
};

/**
 * Message visibility constants
 */
export const MESSAGE_VISIBILITY = {
  PUBLIC: "public",
  PRIVATE: "private",
  INTERNAL: "internal",
};

/**
 * Notification types
 */
export const NOTIFICATION_TYPE = {
  NEW_TICKET: "new_ticket",
  TICKET_ASSIGNED: "ticket_assigned",
  TICKET_ESCALATED: "ticket_escalated",
  TICKET_RESOLVED: "ticket_resolved",
  TICKET_CLOSED: "ticket_closed",
  NEW_MESSAGE: "new_message",

  SLA_WARNING: "sla_warning",
  SLA_BREACH: "sla_breach",
};

/**
 * Email template constants
 */
export const EMAIL_TEMPLATE = Object.freeze({
  TICKET_CREATED: "ticket_created",
  TICKET_STATUS_UPDATED: "ticket_status_updated",
  TICKET_ASSIGNED: "ticket_assigned",
  TICKET_MESSAGE_ADDED: "ticket_message_added",
  TICKET_RESOLVED: "ticket_resolved",
});

/**
 * Email subjects
 */
export const EMAIL_SUBJECT = Object.freeze({
  TICKET_CREATED: "Support Ticket Created - #{ticket_number}",
  TICKET_STATUS_UPDATED: "Support Ticket Status Updated - #{ticket_number}",
  TICKET_ASSIGNED: "Support Ticket Assigned - #{ticket_number}",
  TICKET_MESSAGE_ADDED: "New Message on Support Ticket - #{ticket_number}",
  TICKET_RESOLVED: "Support Ticket Resolved - #{ticket_number}",
});

/**
 * File upload constants
 */
export const FILE_UPLOAD = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_TYPES: [
    "image/jpeg",
    "image/png",
    "image/gif",
    "application/pdf",
    "text/plain",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  ],
  UPLOAD_PATH: "uploads/tickets/",
  ALLOWED_EXTENSIONS: [
    ".jpg",
    ".jpeg",
    ".png",
    ".gif",
    ".pdf",
    ".txt",
    ".doc",
    ".docx",
    ".xls",
    ".xlsx",
  ],
};

/**
 * SLA constants (in hours)
 */
export const SLA_HOURS = {
  NONE: 48,
  LOW: 24,
  MEDIUM: 8,
  HIGH: 4,
  URGENT: 2,
  EMERGENCY: 1,
};

/**
 * Escalation rules
 */
export const ESCALATION_RULES = {
  AUTO_ESCALATE_AFTER_HOURS: 24,
  PRIORITY_ESCALATION_HOURS: {
    EMERGENCY: 1,
    URGENT: 2,
    HIGH: 4,
    MEDIUM: 8,
    LOW: 24,
    NONE: 48,
  },
};

/**
 * Default pagination
 */
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_SIZE: 10,
  MAX_SIZE: 100,
};

/**
 * Cache keys
 */
export const CACHE_KEYS = {
  TICKET_STATS: "ticket_stats",
  USER_TICKETS: "user_tickets",

  KNOWLEDGE_BASE: "knowledge_base",
};

/**
 * Cache TTL (in seconds)
 */
export const CACHE_TTL = {
  SHORT: 300, // 5 minutes
  MEDIUM: 1800, // 30 minutes
  LONG: 3600, // 1 hour
  VERY_LONG: 86400, // 24 hours
};

/**
 * API rate limiting
 */
export const RATE_LIMIT = {
  WINDOW_MS: 15 * 60 * 1000, // 15 minutes
  MAX_REQUESTS: 100,

  FILE_UPLOAD_PER_HOUR: 20,
};

/**
 * Validation constants
 */
export const VALIDATION = {
  TICKET_TITLE_MIN_LENGTH: 5,
  TICKET_TITLE_MAX_LENGTH: 255,
  TICKET_DESCRIPTION_MIN_LENGTH: 10,
  TICKET_DESCRIPTION_MAX_LENGTH: 5000,
  MESSAGE_MIN_LENGTH: 1,
  MESSAGE_MAX_LENGTH: 2000,
  PHONE_REGEX: /^[\+]?[1-9][\d]{0,15}$/,
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
};

/**
 * RabbitMQ Queue constants (following auth-ms pattern)
 */
export const RABBITMQ_QUEUE = Object.freeze({
  // Email queues (shared with auth-ms)
  MAIL_QUEUE: "mail_queue",
  MAIL_FAILED: "mail_failed",
  MAIL_SUCCESS: "mail_success",

  // Support ticket queues
  SUPPORT_TICKET_EMAIL: "support_ticket_email",
  SUPPORT_NOTIFICATION: "support_notification",
  SUPPORT_TICKET_CREATED: "support_ticket_created",
  SUPPORT_TICKET_STATUS_UPDATED: "support_ticket_status_updated",
  SUPPORT_TICKET_ASSIGNED: "support_ticket_assigned",
  SUPPORT_TICKET_MESSAGE_ADDED: "support_ticket_message_added",
  SUPPORT_TICKET_RESOLVED: "support_ticket_resolved",

  // Analytics queues
  SUPPORT_ANALYTICS: "support_analytics",
  TICKET_ANALYTICS: "ticket_analytics",

  // Status update queues
  TICKET_STATUS_UPDATE: "ticket_status_update",
  TICKET_ASSIGNMENT_UPDATE: "ticket_assignment_update",

  // Email notification queues
  EMAIL_NOTIFICATIONS: "email_notifications",
  SUPPORT_EMAIL_QUEUE: "support_email_queue",
});

/**
 * Queue names for RabbitMQ (legacy - keeping for backward compatibility)
 */
export const QUEUE_NAMES = {
  TICKET_CREATED: "ticket.created",
  TICKET_UPDATED: "ticket.updated",
  MESSAGE_CREATED: "message.created",

  ESCALATION: "ticket.escalation",
  NOTIFICATION: "notification.send",
  EMAIL: "email.send",
};

/**
 * Error codes
 */
export const ERROR_CODES = {
  TICKET_NOT_FOUND: "TICKET_NOT_FOUND",
  UNAUTHORIZED_ACCESS: "UNAUTHORIZED_ACCESS",
  INVALID_INPUT: "INVALID_INPUT",

  FILE_UPLOAD_FAILED: "FILE_UPLOAD_FAILED",
  ESCALATION_FAILED: "ESCALATION_FAILED",
  NOTIFICATION_FAILED: "NOTIFICATION_FAILED",
};

/**
 * Success messages
 */
export const SUCCESS_MESSAGES = {
  TICKET_CREATED: "Ticket created successfully",
  TICKET_UPDATED: "Ticket updated successfully",
  MESSAGE_SENT: "Message sent successfully",
  TICKET_ASSIGNED: "Ticket assigned successfully",
  TICKET_ESCALATED: "Ticket escalated successfully",
  TICKET_RESOLVED: "Ticket resolved successfully",
  TICKET_CLOSED: "Ticket closed successfully",
};

export default {
  ROLE_CONSTANT,
  ADMIN_SIDE_USER,
  NORMAL_USER,
  TICKET_STATUS,
  TICKET_PRIORITY,
  TICKET_MODULE,
  TICKET_TYPE,
  MESSAGE_SENDER_TYPE,
  MESSAGE_VISIBILITY,
  NOTIFICATION_TYPE,
  EMAIL_TEMPLATE,
  EMAIL_SUBJECT,
  FILE_UPLOAD,
  SLA_HOURS,
  ESCALATION_RULES,
  PAGINATION,
  CACHE_KEYS,
  CACHE_TTL,
  RATE_LIMIT,
  VALIDATION,
  RABBITMQ_QUEUE,
  QUEUE_NAMES,
  ERROR_CODES,
  SUCCESS_MESSAGES,
};
