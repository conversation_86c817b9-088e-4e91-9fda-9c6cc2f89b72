import { Router } from "express";
import supportMessageController from "../../controller/supportMessage.controller";
import uploadService from "../../helper/upload.service";
import { SUPPORT_FILE_UPLOAD_CONSTANT } from "../../utils/common";
import {
  addMessageValidation,
  getTicketMessagesValidation,
  // TODO: Import these when implementing additional methods
  // addInternalNoteValidation,
  // updateMessageValidation,
  // deleteMessageValidation,
  // searchMessagesValidation,
} from "../../validation/supportMessage.validation";

// Configure S3 Upload for Message Attachments (Following Recipe Pattern)
const multerS3Upload = uploadService.multerS3(
  process.env.NODE_ENV || "development",
  SUPPORT_FILE_UPLOAD_CONSTANT.MESSAGE_ATTACHMENT.folder
);

const router = Router();
//  - using imported controller object

/**
 * @swagger
 * /api/private/support/tickets/{ticket_id}/messages:
 *   get:
 *     tags:
 *       - Support Messages
 *     summary: Get messages for a ticket
 *     description: Retrieve all messages for a specific ticket
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: ticket_id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Ticket ID
 *       - in: query
 *         name: include_private
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Include private/internal messages
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 50
 *         description: Messages per page
 *       - in: query
 *         name: message_type
 *         schema:
 *           type: string
 *           enum: [USER, AGENT, SYSTEM, INTERNAL_NOTE]
 *         description: Filter by message type
 *       - in: query
 *         name: since
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Get messages since this timestamp
 *     responses:
 *       200:
 *         description: Messages retrieved successfully
 *       404:
 *         description: Ticket not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 *   post:
 *     tags:
 *       - Support Messages
 *     summary: Add message to ticket
 *     description: Add a new message to a support ticket
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: ticket_id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Ticket ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - message_text
 *             properties:
 *               message_text:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 10000
 *                 description: Message content
 *               message_type:
 *                 type: string
 *                 enum: [USER, AGENT, SYSTEM, INTERNAL_NOTE]
 *                 default: USER
 *                 description: Type of message
 *               is_private:
 *                 type: boolean
 *                 default: false
 *                 description: Whether message is private
 *               attachment_id:
 *                 type: integer
 *                 description: ID of attached file (from nv_items)
 *     responses:
 *       201:
 *         description: Message added successfully
 *       404:
 *         description: Ticket not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get(
  "/tickets/:ticket_id/messages",
  getTicketMessagesValidation,
  supportMessageController.getTicketMessages
);
router.post(
  "/tickets/:ticket_id/messages",
  multerS3Upload.fields([{ name: "messageFiles", maxCount: 3 }]),
  addMessageValidation,
  supportMessageController.addMessage
);

/**
 * @swagger
 * /api/private/support/tickets/{ticket_id}/internal-notes:
 *   post:
 *     tags:
 *       - Support Messages
 *     summary: Add internal note to ticket
 *     description: Add an internal note that is only visible to agents
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: ticket_id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Ticket ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - message_text
 *             properties:
 *               message_text:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 10000
 *                 description: Internal note content
 *     responses:
 *       201:
 *         description: Internal note added successfully
 *       404:
 *         description: Ticket not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
// TODO: Implement addInternalNote method in controller
// router.post(
//   "/tickets/:ticket_id/internal-notes",
//   addInternalNoteValidation,
//   supportMessageController.addInternalNote
// );

/**
 * @swagger
 * /api/private/support/messages/{id}:
 *   put:
 *     tags:
 *       - Support Messages
 *     summary: Update message
 *     description: Update an existing message (only own messages or admin)
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Message ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               message_text:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 10000
 *                 description: Updated message content
 *               is_private:
 *                 type: boolean
 *                 description: Update privacy setting
 *     responses:
 *       200:
 *         description: Message updated successfully
 *       404:
 *         description: Message not found
 *       403:
 *         description: Not authorized to update this message
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 *   delete:
 *     tags:
 *       - Support Messages
 *     summary: Delete message
 *     description: Soft delete a message (only own messages or admin)
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Message ID
 *     responses:
 *       200:
 *         description: Message deleted successfully
 *       404:
 *         description: Message not found
 *       403:
 *         description: Not authorized to delete this message
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
// TODO: Implement updateMessage and deleteMessage methods in controller
// router.put(
//   "/messages/:id",
//   updateMessageValidation,
//   supportMessageController.updateMessage
// );
// router.delete(
//   "/messages/:id",
//   deleteMessageValidation,
//   supportMessageController.deleteMessage
// );

/**
 * @swagger
 * /api/private/support/tickets/{ticket_id}/messages/search:
 *   get:
 *     tags:
 *       - Support Messages
 *     summary: Search messages within ticket
 *     description: Search for specific content within ticket messages
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: ticket_id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Ticket ID
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *           minLength: 3
 *         description: Search query
 *       - in: query
 *         name: include_private
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Include private messages in search
 *       - in: query
 *         name: message_type
 *         schema:
 *           type: string
 *           enum: [USER, AGENT, SYSTEM, INTERNAL_NOTE]
 *         description: Filter by message type
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 20
 *         description: Results per page
 *     responses:
 *       200:
 *         description: Search results retrieved successfully
 *       400:
 *         description: Invalid search query
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
// TODO: Implement search functionality
// router.get(
//   "/tickets/:ticket_id/messages/search",
//   searchMessagesValidation,
//   supportMessageController.getTicketMessages
// );

export default router;
