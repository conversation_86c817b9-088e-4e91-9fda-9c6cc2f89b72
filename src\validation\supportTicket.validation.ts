import { celebrate, Joi, Segments } from "celebrate";

// Following recipe microservice validation patterns

// Validation for creating tickets (authenticated users only with PIN verification)
export const createTicketValidation = celebrate({
  [Segments.BODY]: Joi.object({
    organization_id: Joi.string().optional().max(50), // Optional - uses user's org from token if not provided
    ticket_owner_user_id: Joi.number().integer().positive().optional(), // For registered users (enables avatar/profile features)
    ticket_owner_name: Joi.string().required().max(100),
    ticket_owner_email: Joi.string().email().required().max(255),
    ticket_owner_phone: Joi.string().optional().max(20),
    ticket_title: Joi.string().required().max(200),
    ticket_description: Joi.string().required(),
    support_pin: Joi.string().required().max(20), // Required PIN for verification
    // module_type removed - will be set by admin, not user
    ticket_type: Joi.string()
      .valid(
        "bug",
        "feature_request",
        "general_query",
        "technical_issue",
        "export_help",
        "account_support",
        "other"
      )
      .default("general_query"),
    ticket_priority: Joi.string()
      .valid("urgent", "high", "medium", "low")
      .default("medium"),
  }),
});

// Validation for updating ticket (Limited to priority only - use comments for other updates)
export const updateTicketValidation = celebrate({
  [Segments.PARAMS]: Joi.object({
    id: Joi.number().integer().required(),
  }),
  [Segments.BODY]: Joi.object({
    ticket_priority: Joi.string()
      .valid("urgent", "high", "medium", "low")
      .optional(),
    // Note: subject and description updates removed - use comment system instead
  }),
});

// Admin-only validation for updating module type
export const updateTicketModuleValidation = celebrate({
  [Segments.PARAMS]: Joi.object({
    id: Joi.number().integer().required(),
  }),
  [Segments.BODY]: Joi.object({
    ticket_module: Joi.string()
      .valid("hrms", "pms", "recipe", "other")
      .required(),
    change_note: Joi.string().optional().max(500),
  }),
});

// Validation for getting tickets with filters
export const getTicketsValidation = celebrate({
  [Segments.QUERY]: Joi.object({
    page: Joi.number().integer().min(1).optional(),
    limit: Joi.number().integer().min(1).max(100).optional(),
    status: Joi.string()
      .valid(
        "open",
        "assigned",
        "in_progress",
        "on_hold",
        "escalated",
        "resolved",
        "closed"
      )
      .optional(),
    priority: Joi.string().valid("urgent", "high", "medium", "low").optional(),
    assigned_to_user_id: Joi.number().integer().optional(),
    organization_id: Joi.string().max(50).optional(),
    search: Joi.string().max(200).optional(),
    sort_by: Joi.string().valid("created_at", "priority", "status").optional(),
    sort_order: Joi.string().valid("ASC", "DESC").optional(),
    unassigned: Joi.string().valid("true", "false").optional(),
    download: Joi.string().valid("excel", "csv").optional(),
  }),
});

// Validation for getting single ticket
export const getTicketByIdValidation = celebrate({
  [Segments.PARAMS]: Joi.object({
    id: Joi.number().integer().required(),
  }),
  [Segments.QUERY]: Joi.object({
    include_private: Joi.boolean().default(false),
  }),
});

// Validation for assigning ticket
export const assignTicketValidation = celebrate({
  [Segments.PARAMS]: Joi.object({
    id: Joi.number().integer().required(),
  }),
  [Segments.BODY]: Joi.object({
    assigned_to_user_id: Joi.number().integer().required(),
    change_note: Joi.string().optional(),
  }),
});

// Validation for updating ticket status
export const updateTicketStatusValidation = celebrate({
  [Segments.PARAMS]: Joi.object({
    id: Joi.number().integer().required(),
  }),
  [Segments.BODY]: Joi.object({
    ticket_status: Joi.string()
      .valid(
        "open",
        "assigned",
        "in_progress",
        "on_hold",
        "escalated",
        "resolved",
        "closed"
      )
      .required(),
    change_note: Joi.string().optional(),
    resolution_note: Joi.string().optional(),
  }),
});

// Validation for adding comments to tickets
export const addCommentValidation = celebrate({
  [Segments.PARAMS]: Joi.object({
    id: Joi.number().integer().required(),
  }),
  [Segments.BODY]: Joi.object({
    message_text: Joi.string().required().min(1).max(5000),
    is_private: Joi.boolean().default(false), // Only agents/admins can create private comments
  }),
});

// Validation for getting ticket comments
export const getCommentsValidation = celebrate({
  [Segments.PARAMS]: Joi.object({
    id: Joi.number().integer().required(),
  }),
  [Segments.QUERY]: Joi.object({
    include_private: Joi.string().valid("true", "false").default("false"),
  }),
});

// Note: Removed unused validation functions to keep only essential ones:
// - rateTicketValidation (not implemented in controller)
// - updateTicketPriorityValidation (not implemented in controller)
// - bulkTicketOperationValidation (not implemented in controller)
// - searchTicketsValidation (not implemented in controller)
// - ticketAnalyticsValidation (not implemented in controller)
// - getTicketStatsValidation (not implemented in controller)
// - escalateTicketValidation (not implemented in controller)
// - closeTicketValidation (not implemented in controller)

// These can be added back when the corresponding controller functions are implemented
