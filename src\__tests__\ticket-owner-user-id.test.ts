import { getTicketOwnerDetails } from '../utils/common';

// Mock the getUser function
jest.mock('../utils/common', () => ({
  ...jest.requireActual('../utils/common'),
  getUser: jest.fn(),
}));

import { getUser } from '../utils/common';

describe('Ticket Owner User ID Implementation', () => {
  const mockGetUser = getUser as jest.MockedFunction<typeof getUser>;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getTicketOwnerDetails', () => {
    it('should return user details with avatar when ticket_owner_user_id exists', async () => {
      // Mock user data
      const mockUserData = {
        id: 123,
        user_first_name: '<PERSON>',
        user_last_name: '<PERSON><PERSON>',
        user_full_name: '<PERSON>',
        user_email: '<EMAIL>',
        user_avatar_link: 'https://example.com/avatar.jpg',
      };

      mockGetUser.mockResolvedValue(mockUserData);

      const mockTicket = {
        ticket_owner_user_id: 123,
        ticket_owner_name: 'Fallback Name',
        ticket_owner_email: '<EMAIL>',
        ticket_owner_phone: '+1234567890',
      };

      const result = await getTicketOwnerDetails(mockTicket);

      expect(result).toEqual({
        user_id: 123,
        name: 'John Doe',
        email: '<EMAIL>',
        avatar_url: 'https://example.com/avatar.jpg',
        is_registered_user: true,
        fallback_name: 'Fallback Name',
        fallback_email: '<EMAIL>',
        fallback_phone: '+1234567890',
      });

      expect(mockGetUser).toHaveBeenCalledWith(123);
    });

    it('should return fallback data when ticket_owner_user_id is null', async () => {
      const mockTicket = {
        ticket_owner_user_id: null,
        ticket_owner_name: 'Guest User',
        ticket_owner_email: '<EMAIL>',
        ticket_owner_phone: '+1234567890',
      };

      const result = await getTicketOwnerDetails(mockTicket);

      expect(result).toEqual({
        user_id: null,
        name: 'Guest User',
        email: '<EMAIL>',
        phone: '+1234567890',
        avatar_url: null,
        is_registered_user: false,
      });

      expect(mockGetUser).not.toHaveBeenCalled();
    });

    it('should return fallback data when user not found', async () => {
      mockGetUser.mockResolvedValue(null);

      const mockTicket = {
        ticket_owner_user_id: 999,
        ticket_owner_name: 'Fallback Name',
        ticket_owner_email: '<EMAIL>',
        ticket_owner_phone: '+1234567890',
      };

      const result = await getTicketOwnerDetails(mockTicket);

      expect(result).toEqual({
        user_id: null,
        name: 'Fallback Name',
        email: '<EMAIL>',
        phone: '+1234567890',
        avatar_url: null,
        is_registered_user: false,
      });

      expect(mockGetUser).toHaveBeenCalledWith(999);
    });

    it('should handle missing ticket data gracefully', async () => {
      const mockTicket = {
        ticket_owner_user_id: null,
        ticket_owner_name: null,
        ticket_owner_email: null,
        ticket_owner_phone: null,
      };

      const result = await getTicketOwnerDetails(mockTicket);

      expect(result).toEqual({
        user_id: null,
        name: 'Unknown User',
        email: '',
        phone: '',
        avatar_url: null,
        is_registered_user: false,
      });
    });

    it('should handle errors gracefully', async () => {
      mockGetUser.mockRejectedValue(new Error('Database error'));

      const mockTicket = {
        ticket_owner_user_id: 123,
        ticket_owner_name: 'Fallback Name',
        ticket_owner_email: '<EMAIL>',
        ticket_owner_phone: '+1234567890',
      };

      const result = await getTicketOwnerDetails(mockTicket);

      expect(result).toEqual({
        user_id: null,
        name: 'Fallback Name',
        email: '<EMAIL>',
        phone: '+1234567890',
        avatar_url: null,
        is_registered_user: false,
      });
    });

    it('should construct name from first and last name when full name is missing', async () => {
      const mockUserData = {
        id: 123,
        user_first_name: 'Jane',
        user_last_name: 'Smith',
        user_full_name: null,
        user_email: '<EMAIL>',
        user_avatar_link: null,
      };

      mockGetUser.mockResolvedValue(mockUserData);

      const mockTicket = {
        ticket_owner_user_id: 123,
        ticket_owner_name: 'Fallback Name',
        ticket_owner_email: '<EMAIL>',
      };

      const result = await getTicketOwnerDetails(mockTicket);

      expect(result.name).toBe('Jane Smith');
      expect(result.is_registered_user).toBe(true);
    });
  });
});
