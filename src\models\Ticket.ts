/**
 * Ticket Model
 *
 * Main entity for support tickets in the customer service system.
 * Handles ticket lifecycle, SLA tracking, escalation, and customer feedback.
 *
 * Features:
 * - Automatic ticket number generation
 * - SLA due date calculation based on priority
 * - Soft delete with paranoid mode
 * - Comprehensive audit trail
 * - Rating and feedback system
 * - Organization-based data isolation
 *
 * <AUTHOR> Service Team
 * @version 1.0.0
 */

import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

// Enums following TTH requirements document
export enum ModuleType {
  HRMS = "hrms",
  PMS = "pms",
  RECIPE = "recipe",
  OTHER = "other",
}

export enum IssueType {
  BUG = "bug",
  FEATURE_REQUEST = "feature_request",
  GENERAL_QUERY = "general_query",
  TECHNICAL_ISSUE = "technical_issue",
  EXPORT_HELP = "export_help",
  ACCOUNT_SUPPORT = "account_support",
  OTHER = "other",
}

export enum Priority {
  URGENT = "urgent",
  HIGH = "high",
  MEDIUM = "medium",
  LOW = "low",
}

export enum TicketStatus {
  OPEN = "open",
  ASSIGNED = "assigned",
  IN_PROGRESS = "in_progress",
  ON_HOLD = "on_hold",
  ESCALATED = "escalated",
  RESOLVED = "resolved",
  CLOSED = "closed",
}

interface TicketAttributes {
  id?: number;
  ticket_number?: string;
  organization_id: string;
  ticket_owner_name: string;
  ticket_owner_email: string;
  ticket_owner_phone?: string;
  ticket_title: string;
  ticket_description: string;
  ticket_module: ModuleType;
  ticket_type: IssueType;
  ticket_priority: Priority;
  ticket_status: TicketStatus;
  // is_private: boolean; // Removed - using organization-based access control

  // Assignment tracking
  assigned_to_user_id?: number;
  assigned_at?: Date;
  assigned_by?: number;

  // Resolution tracking
  resolution_note?: string;
  resolved_at?: Date;
  resolved_by?: number;

  // Rating and review
  rating?: number;
  review_comment?: string;
  reviewed_at?: Date;

  // File attachments - handled via separate ticket_attachments table

  // SLA tracking
  sla_due_date?: Date;
  first_response_at?: Date;

  // TTH Audit fields
  created_by: number;
  updated_by?: number;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date;
}

export default class Ticket
  extends Model<TicketAttributes>
  implements TicketAttributes
{
  public id!: number;
  public ticket_number!: string;
  public organization_id!: string;
  public ticket_owner_name!: string;
  public ticket_owner_email!: string;
  public ticket_owner_phone?: string;
  public ticket_title!: string;
  public ticket_description!: string;
  public ticket_module!: ModuleType;
  public ticket_type!: IssueType;
  public ticket_priority!: Priority;
  public ticket_status!: TicketStatus;
  // public is_private!: boolean; // Removed - using organization-based access control

  // Assignment tracking
  public assigned_to_user_id?: number;
  public assigned_at?: Date;
  public assigned_by?: number;

  // Resolution tracking
  public resolution_note?: string;
  public resolved_at?: Date;
  public resolved_by?: number;

  // Rating and review
  public rating?: number;
  public review_comment?: string;
  public reviewed_at?: Date;

  // File attachments - handled via separate ticket_attachments table

  // SLA tracking
  public sla_due_date?: Date;
  public first_response_at?: Date;

  // TTH Audit fields
  public created_by!: number;
  public updated_by?: number;
  public readonly created_at!: Date;
  public readonly updated_at!: Date;
  public readonly deleted_at?: Date;

  /**
   * Generate unique ticket number for the organization
   * Format: ORG-001-TKT-001 (organization-based sequential numbering)
   *
   * @returns Promise<string> Unique ticket number
   * @business_logic Required for ticket identification and tracking
   */
  public async generateTicketNumber(): Promise<string> {
    const { generateTicketNumber } = await import("../utils/common");
    return generateTicketNumber(this.organization_id);
  }

  /**
   * Check if ticket is overdue based on SLA due date
   *
   * @returns boolean True if current time exceeds SLA due date
   * @business_logic Critical for SLA monitoring and escalation triggers
   */
  public isOverdue(): boolean {
    if (!this.sla_due_date) return false;
    return new Date() > this.sla_due_date;
  }

  /**
   * Determine if ticket can be escalated
   * Only open tickets (not resolved/closed) can be escalated
   *
   * @returns boolean True if ticket is eligible for escalation
   * @business_logic Prevents escalation of already resolved tickets
   */
  public canBeEscalated(): boolean {
    return (
      this.ticket_status !== TicketStatus.CLOSED &&
      this.ticket_status !== TicketStatus.RESOLVED
    );
  }

  /**
   * Check if ticket is in resolved state
   * Includes both RESOLVED and CLOSED statuses
   *
   * @returns boolean True if ticket is resolved or closed
   * @business_logic Used for reporting and workflow decisions
   */
  public isResolved(): boolean {
    return (
      this.ticket_status === TicketStatus.RESOLVED ||
      this.ticket_status === TicketStatus.CLOSED
    );
  }

  static associate(models: any) {
    // Ticket has many messages
    Ticket.hasMany(models.TicketMessage, {
      foreignKey: "ticket_id",
      as: "messages",
    });

    // Ticket has many history records
    Ticket.hasMany(models.TicketHistory, {
      foreignKey: "ticket_id",
      as: "history",
    });

    // Ticket has many attachments (NEW - proper relational structure)
    Ticket.hasMany(models.TicketAttachment, {
      foreignKey: "ticket_id",
      as: "attachments",
    });

    // Soft references to nv_users table (NO Sequelize associations):
    // - created_by references nv_users.id (ticket creator)
    // - assigned_to_user_id references nv_users.id (assigned agent)
    // - assigned_by references nv_users.id (user who assigned the ticket)
    // - resolved_by references nv_users.id (user who resolved the ticket)
    // - updated_by references nv_users.id (last user who updated the ticket)
    //
    // User data (name, avatar, profile) will be fetched from nv_users table via:
    // - Raw SQL queries when needed
    // - Separate service calls to user management functions
    // - Common utility functions like getUser(userId)
    //
    // This approach:
    // ✅ Avoids model dependency issues across microservices
    // ✅ Maintains referential integrity at application level
    // ✅ Follows recipe microservice established patterns
    // ✅ Keeps User model unchanged and common across all MS
  }
}

// Initialize the model
Ticket.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    ticket_number: {
      type: DataTypes.STRING(20),
      allowNull: false,
      unique: true,
      comment: "Unique ticket identifier (e.g., ORG-001-TKT-001)",
    },
    organization_id: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: "Tenant organization identifier",
    },
    ticket_owner_name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: "Name of person submitting the ticket",
    },
    ticket_owner_email: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        isEmail: true,
      },
      comment: "Contact email address",
    },
    ticket_owner_phone: {
      type: DataTypes.STRING(20),
      allowNull: true,
      comment: "Contact phone number",
    },
    ticket_title: {
      type: DataTypes.STRING(200),
      allowNull: false,
      comment: "Ticket title/subject",
    },
    ticket_description: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: "Detailed ticket content",
    },
    ticket_module: {
      type: DataTypes.ENUM("hrms", "pms", "recipe", "other"),
      allowNull: false,
      defaultValue: "other",
      comment: "Functional module",
    },
    ticket_type: {
      type: DataTypes.ENUM(
        "bug",
        "feature_request",
        "general_query",
        "technical_issue",
        "export_help",
        "account_support",
        "other"
      ),
      allowNull: false,
      defaultValue: "general_query",
      comment: "Issue classification",
    },
    ticket_priority: {
      type: DataTypes.ENUM("urgent", "high", "medium", "low"),
      allowNull: false,
      defaultValue: "medium",
      comment: "Urgency level",
    },
    ticket_status: {
      type: DataTypes.ENUM(
        "open",
        "assigned",
        "in_progress",
        "on_hold",
        "escalated",
        "resolved",
        "closed"
      ),
      allowNull: false,
      defaultValue: "open",
      comment: "Status lifecycle",
    },
    // is_private: {
    //   type: DataTypes.BOOLEAN,
    //   allowNull: false,
    //   defaultValue: true,
    //   comment: "Visibility flag - Removed: using organization-based access control",
    // },
    assigned_to_user_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: "Agent assigned to handle ticket",
    },
    assigned_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: "Time of assignment",
    },
    assigned_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: "Who assigned the ticket",
    },
    resolution_note: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: "Final resolution notes",
    },
    resolved_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: "Resolution timestamp",
    },
    resolved_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: "Who resolved the ticket",
    },
    rating: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 1,
        max: 5,
      },
      comment: "User feedback score (1-5)",
    },
    review_comment: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: "User review comment",
    },
    reviewed_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: "When reviewed",
    },

    sla_due_date: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: "SLA deadline",
    },
    first_response_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: "First agent response time",
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: "Who created the ticket",
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: "Last updater",
    },
  },
  {
    sequelize,
    tableName: "mo_support_tickets",
    modelName: "Ticket",
    timestamps: true,
    paranoid: true,
    underscored: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    deletedAt: "deleted_at",
    indexes: [
      {
        fields: ["ticket_number"],
        unique: true,
      },
      {
        fields: ["ticket_status"],
      },
      {
        fields: ["ticket_priority"],
      },
      {
        fields: ["ticket_module"],
      },
      {
        fields: ["assigned_to_user_id"],
      },
      {
        fields: ["organization_id"],
      },
      {
        fields: ["created_at"],
      },
      {
        fields: ["sla_due_date"],
      },
    ],
    hooks: {
      beforeCreate: async (ticket: Ticket) => {
        if (!ticket.ticket_number) {
          ticket.ticket_number = await ticket.generateTicketNumber();
        }
      },
    },
  }
);
