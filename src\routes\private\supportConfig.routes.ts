import { Router } from "express";
import supportConfigController from "../../controller/supportConfig.controller";
import {
  getConfigValidation,
  upsertConfigValidation,
  getAllConfigsValidation,
  deleteConfigValidation,
  toggleSupportValidation,
  defaultConfigValidation,
} from "../../validation/supportConfig.validation";

const router = Router();

/**
 * @swagger
 * /api/private/support/config:
 *   get:
 *     tags:
 *       - Support Configuration
 *     summary: Get all support configurations (Admin only)
 *     description: Retrieve all organization support configurations
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Items per page
 *       - in: query
 *         name: is_active
 *         schema:
 *           type: boolean
 *         description: Filter by active status
 *       - in: query
 *         name: organization_id
 *         schema:
 *           type: string
 *         description: Filter by organization ID
 *     responses:
 *       200:
 *         description: Configurations retrieved successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Admin access required
 *       500:
 *         description: Server error
 */
router.get(
  "/config",
  getAllConfigsValidation,
  supportConfigController.getAllConfigs
);

/**
 * @swagger
 * /api/private/support/config/default:
 *   get:
 *     tags:
 *       - Support Configuration
 *     summary: Get default configuration (Admin only)
 *     description: Get the default configuration that applies to all organizations
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Default configuration retrieved successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Admin access required
 *       404:
 *         description: Default configuration not found
 *       500:
 *         description: Server error
 *   put:
 *     tags:
 *       - Support Configuration
 *     summary: Create or update default configuration (Admin only)
 *     description: Create or update the default configuration for all organizations
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               is_active:
 *                 type: boolean
 *                 default: true
 *                 description: Enable/disable support by default
 *               allow_attachments:
 *                 type: boolean
 *                 default: true
 *                 description: Allow file attachments by default
 *               max_attachment_size:
 *                 type: integer
 *                 minimum: 1024
 *                 maximum: 52428800
 *                 default: 5242880
 *                 description: Maximum attachment size in bytes
 *               allowed_file_types:
 *                 type: array
 *                 items:
 *                   type: string
 *                   enum: [pdf, doc, docx, txt, rtf, png, jpg, jpeg, gif, bmp, webp, xls, xlsx, csv, zip, rar, 7z]
 *                 minItems: 1
 *                 default: [pdf, png, jpg, jpeg, doc, docx]
 *                 description: Allowed file types
 *               auto_assignment_enabled:
 *                 type: boolean
 *                 default: false
 *                 description: Enable automatic ticket assignment
 *               sla_response_time_hours:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 168
 *                 default: 24
 *                 description: SLA response time in hours
 *               sla_resolution_time_hours:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 720
 *                 default: 72
 *                 description: SLA resolution time in hours
 *     responses:
 *       200:
 *         description: Default configuration updated successfully
 *       201:
 *         description: Default configuration created successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Admin access required
 *       500:
 *         description: Server error
 */
router.get("/config/default", supportConfigController.getDefaultConfig);
router.put(
  "/config/default",
  defaultConfigValidation,
  supportConfigController.upsertDefaultConfig
);

/**
 * @swagger
 * /api/private/support/config/{organization_id}:
 *   get:
 *     tags:
 *       - Support Configuration
 *     summary: Get support configuration for organization
 *     description: Get specific organization's support configuration
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: organization_id
 *         required: true
 *         schema:
 *           type: string
 *         description: Organization ID
 *     responses:
 *       200:
 *         description: Configuration retrieved successfully
 *       404:
 *         description: Configuration not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 *   put:
 *     tags:
 *       - Support Configuration
 *     summary: Create or update support configuration
 *     description: Create new or update existing support configuration for organization
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: organization_id
 *         required: true
 *         schema:
 *           type: string
 *         description: Organization ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               support_pin:
 *                 type: string
 *                 minLength: 4
 *                 maxLength: 20
 *                 description: Support PIN for ticket creation
 *               is_active:
 *                 type: boolean
 *                 default: true
 *                 description: Enable/disable support
 *               allow_attachments:
 *                 type: boolean
 *                 default: true
 *                 description: Allow file attachments
 *               max_attachment_size:
 *                 type: integer
 *                 minimum: 1024
 *                 maximum: 52428800
 *                 default: 5242880
 *                 description: Maximum attachment size in bytes
 *               allowed_file_types:
 *                 type: array
 *                 items:
 *                   type: string
 *                   enum: [pdf, doc, docx, txt, rtf, png, jpg, jpeg, gif, bmp, webp, xls, xlsx, csv, zip, rar, 7z]
 *                 minItems: 1
 *                 default: [pdf, png, jpg, jpeg, doc, docx]
 *                 description: Allowed file types
 *               auto_assignment_enabled:
 *                 type: boolean
 *                 default: false
 *                 description: Enable automatic ticket assignment
 *               sla_response_time_hours:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 168
 *                 default: 24
 *                 description: SLA response time in hours
 *               sla_resolution_time_hours:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 720
 *                 default: 72
 *                 description: SLA resolution time in hours
 *     responses:
 *       200:
 *         description: Configuration updated successfully
 *       201:
 *         description: Configuration created successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 *   delete:
 *     tags:
 *       - Support Configuration
 *     summary: Delete support configuration
 *     description: Delete support configuration for organization
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: organization_id
 *         required: true
 *         schema:
 *           type: string
 *         description: Organization ID
 *     responses:
 *       200:
 *         description: Configuration deleted successfully
 *       404:
 *         description: Configuration not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get(
  "/config/:organization_id",
  getConfigValidation,
  supportConfigController.getConfig
);
router.put(
  "/config/:organization_id",
  upsertConfigValidation,
  supportConfigController.upsertConfig
);
router.delete(
  "/config/:organization_id",
  deleteConfigValidation,
  supportConfigController.deleteConfig
);

/**
 * @swagger
 * /api/private/support/config/{organization_id}/toggle:
 *   put:
 *     tags:
 *       - Support Configuration
 *     summary: Toggle support status
 *     description: Enable or disable support for an organization
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: organization_id
 *         required: true
 *         schema:
 *           type: string
 *         description: Organization ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - is_active
 *             properties:
 *               is_active:
 *                 type: boolean
 *                 description: Enable or disable support
 *     responses:
 *       200:
 *         description: Support status updated successfully
 *       404:
 *         description: Configuration not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.put(
  "/config/:organization_id/toggle",
  toggleSupportValidation,
  supportConfigController.toggleSupport
);

export default router;
