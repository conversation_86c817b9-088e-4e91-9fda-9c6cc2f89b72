# Customer Service Ticket System Implementation

## 🎯 **Overview**

This document outlines the complete implementation of the customer service ticket system with support PIN validation and comment-based interaction system.

## 🔐 **Support PIN Requirements**

### **Ticket Creation (PIN Required Every Time)**
- **Route**: `POST /api/v1/support/tickets`
- **PIN Validation**: ✅ **REQUIRED** for every ticket creation
- **Source**: Keycloak organization attributes (`support_pin`)
- **Validation**: Plain text comparison (no bcrypt)
- **Pattern**: Same as auth microservice

### **Ticket Operations (No PIN Required)**
- **Updates**: `PUT /api/v1/private/tickets/:id` - No PIN needed
- **Status Changes**: `PUT /api/v1/private/tickets/:id/status` - No PIN needed
- **Assignment**: `PUT /api/v1/private/tickets/:id/assign` - No PIN needed
- **Comments**: `POST /api/v1/private/tickets/:id/comments` - No PIN needed

## 💬 **Comment System Implementation**

### **Comment Types**

#### **1. Public Comments (Visible to All)**
- **User Comments**: `MessageType.USER`
  - Created by regular users
  - Always public (`is_private: false`)
  - Visible to users, agents, and admins

- **Agent/Admin Public Comments**: `MessageType.AGENT`
  - Created by agents/admins
  - Public by default (`is_private: false`)
  - Visible to users, agents, and admins

#### **2. Internal Comments (Agents/Admins Only)**
- **Internal Notes**: `MessageType.INTERNAL_NOTE`
  - Created by agents/admins only
  - Always private (`is_private: true`)
  - Only visible to agents and admins
  - Hidden from regular users

### **Comment Visibility Rules**

```typescript
// Regular Users
- Can see: Public comments only (is_private: false)
- Cannot see: Internal comments (is_private: true)

// Agents/Admins
- Can see: All comments (public + internal)
- Can create: Both public and internal comments
```

### **API Endpoints**

#### **Add Comment**
```
POST /api/v1/private/tickets/:id/comments
Content-Type: multipart/form-data

Body:
{
  "message_text": "Comment content",
  "is_private": false,  // true for internal comments (agents/admins only)
  "commentFiles": [file] // Optional attachment
}
```

#### **Get Comments**
```
GET /api/v1/private/tickets/:id/comments?include_private=false

Query Parameters:
- include_private: "true" | "false" (agents/admins only can use "true")
```

## 🏗️ **Architecture Patterns**

### **Keycloak Integration**
- **Organization Data**: Fetched from Keycloak (same as auth microservice)
- **Support PIN**: Stored in `organization.attributes.support_pin`
- **Token Management**: Uses Keycloak admin token
- **Data Transformation**: Converts attribute arrays to single values

### **Database Schema**

#### **Tickets Table**: `mo_support_tickets`
- Standard ticket fields
- `organization_id` for scoping
- `created_by`, `updated_by` for audit trail

#### **Messages Table**: `mo_support_ticket_messages`
```sql
- id (Primary Key)
- ticket_id (Foreign Key)
- message_text (TEXT)
- message_type (ENUM: USER, AGENT, SYSTEM, INTERNAL_NOTE)
- is_private (BOOLEAN)
- attachment_id (Foreign Key to nv_items)
- created_by (Foreign Key to nv_users)
- created_at, updated_at, deleted_at
```

## 🔄 **Recommended User Flow**

### **1. Ticket Creation**
```
User → Provides support PIN → System validates against Keycloak → Ticket created
```

### **2. Ongoing Interaction (Recommended)**
```
User/Agent → Adds comments → No PIN required → Real-time communication
```

### **3. Limited Updates**
```
Agent/Admin → Updates priority only → No PIN required → Maintains data integrity
```

## 🛡️ **Security Features**

### **Organization-Level Access Control**
- Support PIN prevents unauthorized ticket creation
- Organization scoping ensures data isolation
- Role-based comment visibility

### **Authentication & Authorization**
- JWT token validation for all operations
- Role-based access (User, Agent, Admin)
- Organization membership verification

## 📊 **Implementation Benefits**

### **✅ Security**
- PIN validation prevents spam/unauthorized tickets
- Internal comments keep sensitive information private
- Organization-scoped access control

### **✅ User Experience**
- Simple ticket creation with PIN
- Chat-like comment system for ongoing interaction
- No repeated PIN entry for updates

### **✅ Data Integrity**
- Immutable ticket core data
- Audit trail through history and comments
- Controlled update permissions

### **✅ Scalability**
- Comment-based interaction scales better
- Reduced database updates
- Clear separation of concerns

## 🔧 **Technical Implementation**

### **Controller Functions**
- `createTicket()` - PIN validation required
- `addTicketComment()` - No PIN required
- `getTicketComments()` - Visibility filtering
- `updateTicket()` - Limited to priority only

### **Validation**
- `createTicketValidation` - Requires support_pin
- `addCommentValidation` - No PIN field
- `getCommentsValidation` - Include private flag

### **Routes**
- `POST /tickets` - Create with PIN
- `POST /tickets/:id/comments` - Add comment
- `GET /tickets/:id/comments` - Get comments
- `PUT /tickets/:id` - Limited update

## 🎉 **Summary**

The implementation provides:

1. **🔐 Secure Ticket Creation**: Support PIN required every time
2. **💬 Flexible Communication**: Comment system for ongoing interaction
3. **👥 Role-Based Visibility**: Public and internal comments
4. **🏢 Organization Scoping**: Proper data isolation
5. **🔄 TTH Consistency**: Same patterns as auth microservice

This approach follows industry best practices for support ticket systems while maintaining the security and consistency requirements of the TTH platform.
