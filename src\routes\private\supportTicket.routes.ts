import { Router } from "express";
import { celebrate, Joi, Segments } from "celebrate";
//  - import from default export
import supportTicketController from "../../controller/supportTicket.controller";
import uploadService from "../../helper/upload.service";
import { SUPPORT_FILE_UPLOAD_CONSTANT } from "../../utils/common";
import {
  getTicketsValidation,
  getTicketByIdValidation,
  assignTicketValidation,
  updateTicketStatusValidation,
  createTicketValidation,
  updateTicketValidation,
  updateTicketModuleValidation,
  addCommentValidation,
  getCommentsValidation,
} from "../../validation/supportTicket.validation";

// Configure S3 Upload for Support Ticket Files (Following Recipe Pattern)
const multerS3Upload = uploadService.multerS3(
  process.env.NODE_ENV || "development",
  SUPPORT_FILE_UPLOAD_CONSTANT.TICKET_ATTACHMENT.folder
);

const router = Router();

/**
 * @swagger
 * /api/v1/private/support/tickets:
 *   get:
 *     tags:
 *       - Support Tickets
 *     summary: Get all tickets with filters
 *     description: Retrieve tickets with pagination and filtering options (organization-scoped)
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Items per page
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [OPEN, ASSIGNED, IN_PROGRESS, ON_HOLD, QA_REVIEW, UNDER_REVIEW, ESCALATED, RESOLVED, CLOSED, INVOICED]
 *         description: Filter by ticket status
 *       - in: query
 *         name: priority
 *         schema:
 *           type: string
 *           enum: [EMERGENCY, URGENT, HIGH, MEDIUM, LOW]
 *         description: Filter by priority
 *       - in: query
 *         name: module_type
 *         schema:
 *           type: string
 *           enum: [HRMS, PMS, OTHER]
 *         description: Filter by module type
 *       - in: query
 *         name: assigned_to_user_id
 *         schema:
 *           type: integer
 *         description: Filter by assigned user
 *       - in: query
 *         name: organization_id
 *         schema:
 *           type: string
 *         description: Filter by organization
 *     responses:
 *       200:
 *         description: Tickets retrieved successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get(
  "/tickets",
  getTicketsValidation,
  supportTicketController.getAllTickets
);

/**
 * @swagger
 * /api/v1/support/tickets:
 *   post:
 *     tags:
 *       - Support Tickets
 *     summary: Create new support ticket with PIN verification
 *     description: Create a new support ticket with support PIN verification and optional file attachments
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - submitter_name
 *               - submitter_email
 *               - subject
 *               - description
 *               - support_pin
 *             properties:
 *               submitter_name:
 *                 type: string
 *                 maxLength: 100
 *                 description: Name of the ticket submitter
 *               submitter_email:
 *                 type: string
 *                 format: email
 *                 maxLength: 255
 *                 description: Email of the ticket submitter
 *               submitter_phone:
 *                 type: string
 *                 maxLength: 20
 *                 description: Phone number of the ticket submitter
 *               subject:
 *                 type: string
 *                 maxLength: 200
 *                 description: Brief subject of the ticket
 *               description:
 *                 type: string
 *                 description: Detailed description of the issue
 *               support_pin:
 *                 type: string
 *                 maxLength: 20
 *                 description: Organization support PIN (required for verification)
 *               issue_type:
 *                 type: string
 *                 enum: [BUG, FEATURE_REQUEST, GENERAL_QUERY, TECHNICAL, NON_TECHNICAL, EXPORT_HELP, SUPPORT, OTHER]
 *                 default: GENERAL_QUERY
 *                 description: Type of issue
 *               priority:
 *                 type: string
 *                 enum: [EMERGENCY, URGENT, HIGH, MEDIUM, LOW]
 *                 default: MEDIUM
 *                 description: Priority level
 *               ticketFiles:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: File attachments (max 5 files, 5MB each)
 *     responses:
 *       201:
 *         description: Ticket created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Ticket created successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     ticket_number:
 *                       type: string
 *                       example: "ORG-001-TKT-001"
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     status:
 *                       type: string
 *                       example: "OPEN"
 *       400:
 *         description: Validation error or missing support PIN
 *       401:
 *         description: Invalid support PIN or unauthorized
 *       404:
 *         description: Support configuration not found
 *       500:
 *         description: Server error
 */
router.post(
  "/tickets",
  multerS3Upload.fields([{ name: "ticketFiles", maxCount: 5 }]),
  createTicketValidation,
  supportTicketController.createTicket
);

/**
 * @swagger
 * /api/private/support/tickets/{id}:
 *   get:
 *     tags:
 *       - Private Support Tickets
 *     summary: Get ticket details with full access
 *     description: Get complete ticket information including private messages and history
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Ticket ID
 *       - in: query
 *         name: include_private
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include private messages
 *     responses:
 *       200:
 *         description: Ticket details retrieved successfully
 *       404:
 *         description: Ticket not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get(
  "/tickets/:id",
  getTicketByIdValidation,
  supportTicketController.getTicketById
);

/**
 * @swagger
 * /api/private/support/tickets/{id}/assign:
 *   put:
 *     tags:
 *       - Private Support Tickets
 *     summary: Assign ticket to agent
 *     description: Assign a ticket to a specific user/agent
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Ticket ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - assigned_to_user_id
 *             properties:
 *               assigned_to_user_id:
 *                 type: integer
 *                 description: ID of user to assign ticket to
 *               change_note:
 *                 type: string
 *                 description: Optional note about the assignment
 *     responses:
 *       200:
 *         description: Ticket assigned successfully
 *       404:
 *         description: Ticket not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.put(
  "/tickets/:id/assign",
  assignTicketValidation,
  supportTicketController.assignTicket
);

/**
 * @swagger
 * /api/private/support/tickets/{id}/status:
 *   put:
 *     tags:
 *       - Private Support Tickets
 *     summary: Update ticket status
 *     description: Change the status of a ticket
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Ticket ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [OPEN, ASSIGNED, IN_PROGRESS, ON_HOLD, QA_REVIEW, UNDER_REVIEW, ESCALATED, RESOLVED, CLOSED, INVOICED]
 *                 description: New status for the ticket
 *               change_note:
 *                 type: string
 *                 description: Optional note about the status change
 *               resolution_note:
 *                 type: string
 *                 description: Resolution notes (required when resolving)
 *     responses:
 *       200:
 *         description: Ticket status updated successfully
 *       404:
 *         description: Ticket not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.put(
  "/tickets/:id/status",
  updateTicketStatusValidation,
  supportTicketController.updateTicketStatus
);

/**
 * @swagger
 * /api/private/support/tickets/{id}:
 *   put:
 *     tags:
 *       - Private Support Tickets
 *     summary: Update ticket with file attachments
 *     description: Update ticket information and add file attachments (Following Recipe Pattern)
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Ticket ID
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               subject:
 *                 type: string
 *                 description: Updated subject
 *               description:
 *                 type: string
 *                 description: Updated description
 *               priority:
 *                 type: string
 *                 enum: [NONE, EMERGENCY, URGENT, HIGH, MEDIUM, LOW]
 *                 description: Updated priority
 *               ticketFiles:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: Additional file attachments
 *     responses:
 *       200:
 *         description: Ticket updated successfully
 *       404:
 *         description: Ticket not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.put(
  "/tickets/:id",
  multerS3Upload.fields([{ name: "ticketFiles", maxCount: 5 }]),
  updateTicketValidation,
  supportTicketController.updateTicket
);

/**
 * @swagger
 * /api/private/support/tickets/{id}/module:
 *   put:
 *     tags:
 *       - Private Support Tickets
 *     summary: Update ticket module type (Admin only)
 *     description: Update the module type of a ticket (Admin access required)
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Ticket ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - module_type
 *             properties:
 *               module_type:
 *                 type: string
 *                 enum: [HRMS, PMS, RECIPE, OTHER]
 *                 description: Module type to assign
 *               change_note:
 *                 type: string
 *                 description: Optional note about the module assignment
 *     responses:
 *       200:
 *         description: Module type updated successfully
 *       403:
 *         description: Access denied (Admin only)
 *       404:
 *         description: Ticket not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.put(
  "/tickets/:id/module",
  updateTicketModuleValidation,
  supportTicketController.updateTicketModule
);

/**
 * @swagger
 * /api/private/support/tickets/{id}/comments:
 *   post:
 *     tags:
 *       - Support Ticket Comments
 *     summary: Add comment/reply to ticket
 *     description: Add a public or private comment to a ticket. Private comments are only visible to agents/admins.
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Ticket ID
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - message_text
 *             properties:
 *               message_text:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 5000
 *                 description: Comment text content
 *               is_private:
 *                 type: boolean
 *                 default: false
 *                 description: Whether this is an internal comment (agents/admins only)
 *               commentFiles:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: Optional file attachment for the comment
 *     responses:
 *       201:
 *         description: Comment added successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Ticket not found
 *       500:
 *         description: Server error
 */
router.post(
  "/tickets/:id/comments",
  multerS3Upload.fields([{ name: "commentFiles", maxCount: 1 }]),
  addCommentValidation,
  supportTicketController.addTicketComment
);

/**
 * @swagger
 * /api/private/support/tickets/{id}/comments:
 *   get:
 *     tags:
 *       - Support Ticket Comments
 *     summary: Get ticket comments
 *     description: Retrieve all comments for a ticket. Private comments are only visible to agents/admins.
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Ticket ID
 *       - in: query
 *         name: include_private
 *         schema:
 *           type: string
 *           enum: ["true", "false"]
 *           default: "false"
 *         description: Include private comments (agents/admins only)
 *     responses:
 *       200:
 *         description: Comments retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Comments fetched successfully"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       message_text:
 *                         type: string
 *                       message_type:
 *                         type: string
 *                         enum: [USER, AGENT, SYSTEM, INTERNAL_NOTE]
 *                       is_private:
 *                         type: boolean
 *                       created_by:
 *                         type: integer
 *                       created_at:
 *                         type: string
 *                         format: date-time
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Ticket not found
 *       500:
 *         description: Server error
 */
router.get(
  "/tickets/:id/comments",
  getCommentsValidation,
  supportTicketController.getTicketComments
);

/**
 * @swagger
 * /api/private/support/tickets/{id}/conversation:
 *   get:
 *     tags:
 *       - Support Ticket Conversation
 *     summary: Get ticket conversation (API-based, replaces websocket)
 *     description: Get paginated conversation messages for a ticket
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Ticket ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: size
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 20
 *         description: Messages per page
 *       - in: query
 *         name: include_private
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Include private messages (agents/admins only)
 *     responses:
 *       200:
 *         description: Conversation retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Messages fetched successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     ticket:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: integer
 *                         ticket_number:
 *                           type: string
 *                         subject:
 *                           type: string
 *                         status:
 *                           type: string
 *                     conversation:
 *                       type: object
 *                       properties:
 *                         data:
 *                           type: array
 *                           items:
 *                             type: object
 *                         pagination:
 *                           type: object
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Ticket not found
 *       500:
 *         description: Server error
 */
router.get(
  "/tickets/:id/conversation",
  getTicketByIdValidation,
  supportTicketController.getConversation
);

/**
 * @swagger
 * /api/private/support/tickets/{id}/conversation:
 *   post:
 *     tags:
 *       - Support Ticket Conversation
 *     summary: Send message in conversation (API-based, replaces websocket)
 *     description: Send a message in the ticket conversation with optional file attachment
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Ticket ID
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - message_text
 *             properties:
 *               message_text:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 5000
 *                 description: Message content
 *               is_private:
 *                 type: boolean
 *                 default: false
 *                 description: Whether this is an internal message (agents/admins only)
 *               messageFiles:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: Optional file attachment for the message
 *     responses:
 *       201:
 *         description: Message sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Message created successfully"
 *                 data:
 *                   type: object
 *                   description: Created message object
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Ticket not found
 *       500:
 *         description: Server error
 */
router.post(
  "/tickets/:id/conversation",
  multerS3Upload.fields([{ name: "messageFiles", maxCount: 1 }]),
  addCommentValidation,
  supportTicketController.sendConversationMessage
);

/**
 * @swagger
 * /api/private/support/assignable-users:
 *   get:
 *     tags:
 *       - Support Ticket Management
 *     summary: Get assignable users for tickets (integrated user management)
 *     description: Get users who can be assigned to support tickets with filtering
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for user name or email
 *       - in: query
 *         name: roleFilter
 *         schema:
 *           type: string
 *         description: Filter by user role
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Items per page
 *     responses:
 *       200:
 *         description: Assignable users retrieved successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       500:
 *         description: Server error
 */
router.get(
  "/assignable-users",
  supportTicketController.getAssignableUsersForTickets
);



/**
 * @swagger
 * /api/private/support/tickets/{id}/feedback:
 *   post:
 *     tags:
 *       - Support Ticket Management
 *     summary: Submit feedback and rating for a ticket
 *     description: Submit customer feedback and rating for a resolved/closed ticket
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Ticket ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - rating
 *             properties:
 *               rating:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 5
 *                 description: Rating from 1 to 5
 *                 example: 4
 *               review_comment:
 *                 type: string
 *                 maxLength: 1000
 *                 description: Optional feedback comment
 *                 example: "Great support, issue resolved quickly"
 *     responses:
 *       200:
 *         description: Feedback submitted successfully
 *       400:
 *         description: Validation error or ticket not eligible for feedback
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Ticket not found
 *       500:
 *         description: Server error
 */
router.post(
  "/tickets/:id/feedback",
  celebrate({
    [Segments.PARAMS]: Joi.object({
      id: Joi.number().integer().required(),
    }),
    [Segments.BODY]: Joi.object({
      rating: Joi.number().integer().min(1).max(5).required(),
      review_comment: Joi.string().max(1000).allow("").optional(),
    }),
  }),
  supportTicketController.submitTicketFeedback
);

/**
 * @swagger
 * /api/private/support/feedback/stats:
 *   get:
 *     tags:
 *       - Support Ticket Management
 *     summary: Get feedback statistics
 *     description: Get feedback and rating statistics for the organization
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: organization_id
 *         schema:
 *           type: string
 *         description: Organization ID (admin only)
 *       - in: query
 *         name: date_from
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for filtering
 *       - in: query
 *         name: date_to
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for filtering
 *     responses:
 *       200:
 *         description: Feedback statistics retrieved successfully
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get(
  "/feedback/stats",
  celebrate({
    [Segments.QUERY]: Joi.object({
      organization_id: Joi.string().optional(),
      date_from: Joi.date().iso().optional(),
      date_to: Joi.date().iso().optional(),
    }),
  }),
  supportTicketController.getFeedbackStats
);

/**
 * @route GET /api/private/support/export/excel
 * @description Export tickets to Excel using getAllTickets with download=excel
 * @access Private
 */
router.get("/export/excel", (req, res) => {
  req.query.download = "excel";
  return supportTicketController.getAllTickets(req, res);
});

/**
 * @route GET /api/private/support/export/csv
 * @description Export tickets to CSV using getAllTickets with download=csv
 * @access Private
 */
router.get("/export/csv", (req, res) => {
  req.query.download = "csv";
  return supportTicketController.getAllTickets(req, res);
});

export default router;
