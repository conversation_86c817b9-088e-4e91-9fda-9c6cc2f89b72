import { celebrate, Joi, Segments } from "celebrate";

// Validation for admin dashboard
export const adminDashboardValidation = celebrate({
  [Segments.QUERY]: Joi.object({
    organization_id: Joi.string().max(50).optional(),
  }),
});

// Validation for admin tickets with advanced filters
export const adminTicketsValidation = celebrate({
  [Segments.QUERY]: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(10),
    search: Joi.string().optional().min(1).max(255),
    status: Joi.alternatives()
      .try(
        Joi.string().valid(
          "open",
          "assigned",
          "in_progress",
          "on_hold",
          "escalated",
          "resolved",
          "closed"
        ),
        Joi.array().items(
          Joi.string().valid(
            "open",
            "assigned",
            "in_progress",
            "on_hold",
            "escalated",
            "resolved",
            "closed"
          )
        )
      )
      .optional(),
    priority: Joi.alternatives()
      .try(
        Joi.string().valid(
          "urgent",
          "high",
          "medium",
          "low"
        ),
        Joi.array().items(
          Joi.string().valid(
            "urgent",
            "high",
            "medium",
            "low"
          )
        )
      )
      .optional(),
    ticket_module: Joi.string().valid("hrms", "pms", "recipe", "other").optional(),
    ticket_type: Joi.string()
      .valid(
        "bug",
        "feature_request",
        "general_query",
        "technical_issue",
        "export_help",
        "account_support",
        "other"
      )
      .optional(),
    assigned_to_user_id: Joi.alternatives()
      .try(Joi.number().integer(), Joi.string().valid("null"))
      .optional(),
    organization_id: Joi.string().max(50).optional(),
    ticket_owner_email: Joi.string().email().optional(),
    date_from: Joi.date().iso().optional(),
    date_to: Joi.date().iso().min(Joi.ref("date_from")).optional(),
    overdue: Joi.boolean().optional(),
    unassigned: Joi.boolean().optional(),
    has_rating: Joi.boolean().optional(),
    sort_by: Joi.string()
      .valid(
        "created_at",
        "updated_at",
        "ticket_number",
        "ticket_title",
        "ticket_priority",
        "ticket_status",
        "ticket_owner_name",
        "assigned_at",
        "resolved_at",
        "sla_due_date"
      )
      .default("created_at"),
    sort_order: Joi.string().valid("ASC", "DESC").default("DESC"),
  }),
});

// Validation for bulk ticket operations
export const bulkTicketOperationValidation = celebrate({
  [Segments.BODY]: Joi.object({
    ticket_ids: Joi.array()
      .items(Joi.number().integer())
      .min(1)
      .max(100)
      .required(),
    operation: Joi.string()
      .valid("assign", "status_update", "priority_update", "delete")
      .required(),
    data: Joi.object().when("operation", {
      switch: [
        {
          is: "assign",
          then: Joi.object({
            assigned_to_user_id: Joi.number().integer().required(),
            change_note: Joi.string().optional().max(500),
          }).required(),
        },
        {
          is: "status_update",
          then: Joi.object({
            status: Joi.string()
              .valid(
                "open",
                "assigned",
                "in_progress",
                "on_hold",
                "escalated",
                "resolved",
                "closed"
              )
              .required(),
            change_note: Joi.string().optional().max(500),
            resolution_note: Joi.string().optional().max(1000),
          }).required(),
        },
        {
          is: "priority_update",
          then: Joi.object({
            priority: Joi.string()
              .valid("urgent", "high", "medium", "low")
              .required(),
            change_note: Joi.string().optional().max(500),
          }).required(),
        },
        {
          is: "delete",
          then: Joi.object({
            change_note: Joi.string().optional().max(500),
          }).optional(),
        },
      ],
    }),
  }),
});

// Validation for ticket analytics (used in admin routes)
export const ticketAnalyticsValidation = celebrate({
  [Segments.QUERY]: Joi.object({
    organization_id: Joi.string().max(50).optional(),
    period: Joi.string().valid("7d", "30d", "90d", "1y").default("30d"),
    date_from: Joi.date().iso().optional(),
    date_to: Joi.date().iso().min(Joi.ref("date_from")).optional(),
  }),
});

// Validation for admin user management
export const adminUserValidation = celebrate({
  [Segments.QUERY]: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(10),
    search: Joi.string().optional().min(1).max(255),
    role: Joi.string().valid("admin", "agent", "user").optional(),
    organization_id: Joi.string().max(50).optional(),
    is_active: Joi.boolean().optional(),
    sort_by: Joi.string()
      .valid("created_at", "updated_at", "name", "email", "last_login")
      .default("created_at"),
    sort_order: Joi.string().valid("ASC", "DESC").default("DESC"),
  }),
});

// Validation for admin organization management
export const adminOrganizationValidation = celebrate({
  [Segments.QUERY]: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(10),
    search: Joi.string().optional().min(1).max(255),
    is_active: Joi.boolean().optional(),
    has_support: Joi.boolean().optional(),
    sort_by: Joi.string()
      .valid("created_at", "updated_at", "name", "ticket_count")
      .default("created_at"),
    sort_order: Joi.string().valid("ASC", "DESC").default("DESC"),
  }),
});

// Validation for admin reports
export const adminReportsValidation = celebrate({
  [Segments.QUERY]: Joi.object({
    report_type: Joi.string()
      .valid(
        "ticket_summary",
        "sla_performance",
        "agent_performance",
        "customer_satisfaction",
        "resolution_trends",
        "workload_distribution"
      )
      .required(),
    start_date: Joi.date().iso().required(),
    end_date: Joi.date().iso().min(Joi.ref("start_date")).required(),
    organization_id: Joi.string().max(50).optional(),
    format: Joi.string().valid("json", "csv", "pdf", "excel").default("json"),
    include_charts: Joi.boolean().default(false),
    group_by: Joi.string()
      .valid("day", "week", "month", "quarter")
      .default("day"),
  }),
});

// Validation for admin system settings
export const adminSystemSettingsValidation = celebrate({
  [Segments.BODY]: Joi.object({
    setting_key: Joi.string().required().max(100),
    setting_value: Joi.alternatives()
      .try(Joi.string(), Joi.number(), Joi.boolean(), Joi.object(), Joi.array())
      .required(),
    description: Joi.string().optional().max(500),
    is_public: Joi.boolean().default(false),
    requires_restart: Joi.boolean().default(false),
  }),
});

// Validation for admin audit logs
export const adminAuditLogsValidation = celebrate({
  [Segments.QUERY]: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(10),
    action: Joi.string().optional(),
    user_id: Joi.number().integer().optional(),
    organization_id: Joi.string().max(50).optional(),
    resource_type: Joi.string()
      .valid("ticket", "message", "config", "user", "organization")
      .optional(),
    resource_id: Joi.string().optional(),
    start_date: Joi.date().iso().optional(),
    end_date: Joi.date().iso().min(Joi.ref("start_date")).optional(),
    sort_by: Joi.string()
      .valid("created_at", "action", "user_id")
      .default("created_at"),
    sort_order: Joi.string().valid("ASC", "DESC").default("DESC"),
  }),
});

// Validation for admin export operations
export const adminExportValidation = celebrate({
  [Segments.BODY]: Joi.object({
    export_type: Joi.string()
      .valid("tickets", "messages", "users", "organizations", "analytics")
      .required(),
    format: Joi.string().valid("csv", "excel", "json", "pdf").default("csv"),
    filters: Joi.object().optional(),
    include_deleted: Joi.boolean().default(false),
    date_range: Joi.object({
      start_date: Joi.date().iso().required(),
      end_date: Joi.date().iso().min(Joi.ref("start_date")).required(),
    }).optional(),
    fields: Joi.array().items(Joi.string()).optional(),
    email_to: Joi.string().email().optional(),
  }),
});
