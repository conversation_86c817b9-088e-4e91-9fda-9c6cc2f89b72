import { celebrate, Joi, Segments } from "celebrate";

// Validation for getting support configuration
export const getConfigValidation = celebrate({
  [Segments.PARAMS]: Joi.object({
    organization_id: Joi.string().required().max(50),
  }),
});

// Validation for creating/updating support configuration
export const upsertConfigValidation = celebrate({
  [Segments.PARAMS]: Joi.object({
    organization_id: Joi.string().required().max(50),
  }),
  [Segments.BODY]: Joi.object({
    support_pin: Joi.string().optional().min(4).max(20),
    is_active: Joi.boolean().default(true),
    allow_attachments: Joi.boolean().default(true),
    max_attachment_size: Joi.number()
      .integer()
      .min(1024)
      .max(52428800)
      .default(5242880), // 1KB to 50MB
    allowed_file_types: Joi.array()
      .items(
        Joi.string().valid(
          "pdf",
          "doc",
          "docx",
          "txt",
          "rtf",
          "png",
          "jpg",
          "jpeg",
          "gif",
          "bmp",
          "webp",
          "xls",
          "xlsx",
          "csv",
          "zip",
          "rar",
          "7z"
        )
      )
      .min(1)
      .default(["pdf", "png", "jpg", "jpeg", "doc", "docx"]),
    auto_assignment_enabled: Joi.boolean().default(false),
    sla_response_time_hours: Joi.number().integer().min(1).max(168).default(24), // 1 hour to 1 week
    sla_resolution_time_hours: Joi.number()
      .integer()
      .min(1)
      .max(720)
      .default(72), // 1 hour to 1 month
  }),
});

// Validation for PIN validation
export const validatePinValidation = celebrate({
  [Segments.PARAMS]: Joi.object({
    organization_id: Joi.string().required().max(50),
  }),
  [Segments.BODY]: Joi.object({
    support_pin: Joi.string().required().max(20),
  }),
});

// Validation for getting all configurations (admin)
export const getAllConfigsValidation = celebrate({
  [Segments.QUERY]: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(10),
    is_active: Joi.boolean().optional(),
    organization_id: Joi.string().max(50).optional(),
  }),
});

// Validation for deleting configuration
export const deleteConfigValidation = celebrate({
  [Segments.PARAMS]: Joi.object({
    organization_id: Joi.string().required().max(50),
  }),
});

// Validation for toggling support status
export const toggleSupportValidation = celebrate({
  [Segments.PARAMS]: Joi.object({
    organization_id: Joi.string().required().max(50),
  }),
  [Segments.BODY]: Joi.object({
    is_active: Joi.boolean().required(),
  }),
});

// Validation for default configuration (admin only)
export const defaultConfigValidation = celebrate({
  [Segments.BODY]: Joi.object({
    is_active: Joi.boolean().default(true),
    allow_attachments: Joi.boolean().default(true),
    max_attachment_size: Joi.number()
      .integer()
      .min(1024)
      .max(52428800)
      .default(5242880), // 1KB to 50MB
    allowed_file_types: Joi.array()
      .items(
        Joi.string().valid(
          "pdf",
          "doc",
          "docx",
          "txt",
          "rtf",
          "png",
          "jpg",
          "jpeg",
          "gif",
          "bmp",
          "webp",
          "xls",
          "xlsx",
          "csv",
          "zip",
          "rar",
          "7z"
        )
      )
      .min(1)
      .default(["pdf", "png", "jpg", "jpeg", "doc", "docx"]),
    auto_assignment_enabled: Joi.boolean().default(false),
    sla_response_time_hours: Joi.number().integer().min(1).max(168).default(24), // 1 hour to 1 week
    sla_resolution_time_hours: Joi.number()
      .integer()
      .min(1)
      .max(720)
      .default(72), // 1 hour to 1 month
  }),
});
