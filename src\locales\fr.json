{"UNAUTHORIZED_ACCESS": "Accès non autorisé", "INVALID_TOKEN": "<PERSON><PERSON> invalide ou expiré", "USER_NOT_FOUND": "Utilisateur non trouvé", "AUTHENTICATION_FAILED": "Échec de l'authentification", "INSUFFICIENT_PERMISSIONS": "Permissions insuffisantes", "AUTHORIZATION_FAILED": "Échec de l'autorisation", "VALIDATION_ERROR": "Erreur de validation", "REQUIRED_FIELDS_MISSING": "Les champs obligatoires sont manquants", "TICKET_CREATED_SUCCESSFULLY": "Ticket de support créé avec succès", "TICKET_CREATION_FAILED": "Échec de la création du ticket de support", "TICKETS_FETCHED_SUCCESSFULLY": "Tickets récupérés avec succès", "TICKETS_FETCH_FAILED": "Échec de la récupération des tickets", "TICKET_FETCHED_SUCCESSFULLY": "Ticket récupéré avec succès", "TICKET_FETCH_FAILED": "Échec de la récupération du ticket", "TICKET_NOT_FOUND": "Ticket non trouvé", "TICKET_UPDATED_SUCCESSFULLY": "Ticket mis à jour avec succès", "TICKET_UPDATE_FAILED": "Échec de la mise à jour du ticket", "TICKET_ASSIGNED_SUCCESSFULLY": "Ticket assigné avec succès", "TICKET_ASSIGNMENT_FAILED": "Échec de l'assignation du ticket", "TICKET_ESCALATED_SUCCESSFULLY": "Ticket escaladé avec succès", "TICKET_ESCALATION_FAILED": "Échec de l'escalade du ticket", "MESSAGE_SENT_SUCCESSFULLY": "Message envoy<PERSON> avec succès", "MESSAGE_SEND_FAILED": "Échec de l'envoi du message", "MESSAGES_FETCHED_SUCCESSFULLY": "Messages récupérés avec succès", "MESSAGES_FETCH_FAILED": "Échec de la récupération des messages", "MESSAGE_NOT_FOUND": "Message non trouvé", "MESSAGE_UPDATED_SUCCESSFULLY": "Message mis à jour avec succès", "MESSAGE_UPDATE_FAILED": "Échec de la mise à jour du message", "MESSAGE_DELETED_SUCCESSFULLY": "Message supprimé avec succès", "MESSAGE_DELETE_FAILED": "Échec de la suppression du message", "MESSAGE_MARKED_AS_SOLUTION": "Message marqué comme solution", "MESSAGE_RATING_SAVED": "Évaluation du message sauvegardée avec succès", "AI_RESPONSE_GENERATED": "Réponse IA générée avec succès", "AI_SERVICE_UNAVAILABLE": "Le service IA est actuellement indisponible", "AI_ANALYSIS_COMPLETED": "Analyse IA terminée", "AI_ANALYSIS_FAILED": "Échec de l'analyse IA", "ESCALATION_COMPLETED": "Escalade terminée avec succès", "ESCALATION_FAILED": "Échec de l'escalade", "NOTIFICATION_SENT": "Notification envoyée avec succès", "NOTIFICATION_FAILED": "Échec de l'envoi de la notification", "RATE_LIMIT_EXCEEDED": "Limite de taux dépassée. Veuillez réessayer plus tard", "AI_RATE_LIMIT_EXCEEDED": "Limite de requêtes IA dépassée. Veuillez réessayer plus tard", "FILE_UPLOAD_RATE_LIMIT_EXCEEDED": "Limite de téléchargement de fichiers dépassée. Veuillez réessayer plus tard", "STRICT_RATE_LIMIT_EXCEEDED": "Limite de taux dépassée pour cette opération sensible", "INVALID_FILE_TYPE": "Type de fichier invalide. Veuillez télécharger un format de fichier pris en charge", "FILE_TOO_LARGE": "La taille du fichier dépasse la limite maximale autorisée", "FILE_UPLOAD_FAILED": "Échec du téléchargement du fichier", "FILE_UPLOAD_SUCCESSFUL": "<PERSON><PERSON><PERSON> téléchargé avec succès", "SEARCH_COMPLETED": "Recherche terminée avec succès", "SEARCH_FAILED": "Échec de l'opération de recherche", "ANALYTICS_FETCHED": "Données d'analyse récupérées avec succès", "ANALYTICS_FETCH_FAILED": "Échec de la récupération des données d'analyse", "CONFIG_UPDATED": "Configuration mise à jour avec succès", "CONFIG_UPDATE_FAILED": "Échec de la mise à jour de la configuration", "CONFIG_FETCHED": "Configuration récupérée avec succès", "CONFIG_FETCH_FAILED": "Échec de la récupération de la configuration", "DASHBOARD_DATA_FETCHED": "Données du tableau de bord récupérées avec succès", "DASHBOARD_DATA_FETCH_FAILED": "Échec de la récupération des données du tableau de bord", "SYSTEM_STATUS_FETCHED": "État du système récupéré avec succès", "SYSTEM_STATUS_FETCH_FAILED": "Échec de la récupération de l'état du système", "KNOWLEDGE_BASE_UPDATED": "Base de connaissances mise à jour avec succès", "KNOWLEDGE_BASE_UPDATE_FAILED": "Échec de la mise à jour de la base de connaissances", "SUGGESTIONS_FETCHED": "Suggestions récupérées avec succès", "SUGGESTIONS_FETCH_FAILED": "Échec de la récupération des suggestions", "AI_TEST_COMPLETED": "Test IA terminé avec succès", "AI_TEST_FAILED": "Échec du test IA", "AI_RETRAIN_INITIATED": "Réentraînement IA initié avec succès", "AI_RETRAIN_FAILED": "Échec de l'initiation du réentraînement IA", "SLA_WARNING": "Échéance SLA approche", "SLA_BREACH": "Échéance SLA dépassée", "TICKET_RESOLVED": "Le ticket a été résolu", "TICKET_CLOSED": "Le ticket a été fermé", "CUSTOMER_SATISFACTION_RECORDED": "Évaluation de satisfaction client enregistrée", "EXPERT_ASSIGNED": "Un expert a été assigné à votre ticket", "AUTO_ESCALATION": "Le ticket a été automatiquement escaladé", "MANUAL_ESCALATION": "Le ticket a été manuellement escaladé", "AI_CONFIDENCE_LOW": "La confiance de l'IA est faible, considérez une révision humaine", "AI_CONFIDENCE_HIGH": "L'IA est confiante dans cette réponse", "SENTIMENT_NEGATIVE": "Sentiment négatif détecté dans le message du client", "SENTIMENT_POSITIVE": "Sentiment positif détecté dans le message du client", "EMOTION_DETECTED": "Émotion du client détectée", "PRIORITY_ESCALATED": "La priorité du ticket a été escaladée", "ORGANIZATION_ACCESS_DENIED": "Accès refusé pour cette organisation", "SUPER_ADMIN_REQUIRED": "Privilèges de super administrateur requis", "ADMIN_REQUIRED": "Privilèges d'administrateur requis", "AGENT_REQUIRED": "Privilèges d'agent requis", "TICKET_OWNER_ONLY": "Seul le propriétaire du ticket peut effectuer cette action", "INTERNAL_SERVER_ERROR": "<PERSON>rreur interne du serveur", "SERVICE_UNAVAILABLE": "Service temporairement indisponible", "MAINTENANCE_MODE": "Le système est en maintenance", "FEATURE_DISABLED": "Cette fonctionnalité est actuellement désactivée", "INVALID_REQUEST": "Format de requête invalide", "MISSING_PARAMETERS": "Paramètres requis manquants", "INVALID_PARAMETERS": "Valeurs de paramètres invalides fournies", "OPERATION_SUCCESSFUL": "Opération terminée avec succès", "OPERATION_FAILED": "Échec de l'opération", "DATA_NOT_FOUND": "Données demandées non trouvées", "DUPLICATE_ENTRY": "Entrée en double détectée", "CONFLICT_ERROR": "Conflit avec les données existantes", "PRECONDITION_FAILED": "Précondition échouée", "RESOURCE_LOCKED": "La ressource est actuellement verrouillée", "QUOTA_EXCEEDED": "Limite de quota dépassée", "SESSION_EXPIRED": "La session a expiré", "ACCOUNT_SUSPENDED": "Le compte a été suspendu", "ACCOUNT_LOCKED": "Le compte est temporairement verrouillé", "PASSWORD_EXPIRED": "Le mot de passe a expiré", "TWO_FACTOR_REQUIRED": "Authentification à deux facteurs requise", "EMAIL_VERIFICATION_REQUIRED": "Vérification par e-mail requise", "PHONE_VERIFICATION_REQUIRED": "Vérification par téléphone requise", "TERMS_ACCEPTANCE_REQUIRED": "Acceptation des termes et conditions requise", "PRIVACY_POLICY_ACCEPTANCE_REQUIRED": "Acceptation de la politique de confidentialité requise", "GDPR_CONSENT_REQUIRED": "Consentement RGPD requis", "DATA_RETENTION_POLICY": "La politique de rétention des données s'applique", "BACKUP_COMPLETED": "Sauvegarde terminée avec succès", "BACKUP_FAILED": "Échec de l'opération de sauvegarde", "RESTORE_COMPLETED": "Restauration terminée avec succès", "RESTORE_FAILED": "Échec de l'opération de restauration", "SYNC_COMPLETED": "Synchronisation terminée avec succès", "SYNC_FAILED": "Échec de la synchronisation", "MIGRATION_COMPLETED": "Migration terminée avec succès", "MIGRATION_FAILED": "Échec de la migration", "HEALTH_CHECK_PASSED": "Vérification de santé réussie", "HEALTH_CHECK_FAILED": "Échec de la vérification de santé", "PERFORMANCE_DEGRADED": "Dégradation des performances détectée", "PERFORMANCE_OPTIMAL": "Les performances du système sont optimales", "SECURITY_ALERT": "Al<PERSON>e de sécurité déclenchée", "SECURITY_BREACH": "Violation de sécurité détectée", "AUDIT_LOG_CREATED": "Entrée de journal d'audit créée", "COMPLIANCE_CHECK_PASSED": "Vérification de conformité réussie", "COMPLIANCE_CHECK_FAILED": "Échec de la vérification de conformité", "SOMETHING_WENT_WRONG": "<PERSON><PERSON><PERSON> chose s'est mal passé", "SUCCESS_DATA_FETCHED": "Données récupérées avec succès", "SUCCESS_DATA_CREATED": "Données créées avec succès", "SUCCESS_DATA_UPDATED": "<PERSON>n<PERSON> mises à jour avec succès", "SUCCESS_DATA_RETRIEVED": "Données récupérées avec succès", "ERROR_CREATING_TICKET": "Erreur lors de la création du ticket", "ERROR_UPDATING_TICKET": "Erreur lors de la mise à jour du ticket", "ERROR_DELETING_TICKET": "<PERSON><PERSON><PERSON> lors de la suppression du ticket", "ERROR_ASSIGNING_TICKET": "Erreur lors de l'assignation du ticket", "ERROR_UPDATING_TICKET_STATUS": "Erreur lors de la mise à jour du statut du ticket", "ERROR_FETCHING_TICKETS": "Erreur lors de la récupération des tickets", "ERROR_FETCHING_TICKET": "Erreur lors de la récupération du ticket", "TICKET_STATUS_UPDATED": "Statut du ticket mis à jour avec succès", "PERMISSION_DENIED": "Permission refusée", "ACCESS_DENIED": "<PERSON><PERSON>ès refusé", "FORBIDDEN": "Interdit", "BAD_REQUEST": "<PERSON><PERSON><PERSON><PERSON> requ<PERSON>", "NOT_FOUND": "Non trouvé", "METHOD_NOT_ALLOWED": "Méthode non autorisée", "CONFLICT": "Conflit survenu", "UNPROCESSABLE_ENTITY": "Entité non traitable", "TOO_MANY_REQUESTS": "Trop de requêtes", "DATABASE_ERROR": "Erreur de base de données", "DATABASE_CONNECTION_ERROR": "Erreur de connexion à la base de données", "FILE_UPLOAD_ERROR": "<PERSON><PERSON><PERSON> de téléchargement de fi<PERSON>er", "FILE_SIZE_TOO_LARGE": "La taille du fichier est trop grande", "FILE_TYPE_NOT_ALLOWED": "Type de fichier non autorisé", "INVALID_FILE_FORMAT": "Format de fichier invalide", "NO_FILE_UPLOADED": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FILES_UPLOADED_SUCCESSFULLY": "Fichiers téléchargés avec succès", "OPERATION_NOT_ALLOWED": "Opération non autorisée", "RESOURCE_NOT_FOUND": "Ressource non trouvée", "INVALID_DATA_FORMAT": "Format de données invalide", "MISSING_REQUIRED_FIELDS": "Champs obligatoires manquants", "ORGANIZATION_ID_REQUIRED": "ID d'organisation requis", "TICKET_ID_REQUIRED": "ID de ticket requis", "MESSAGE_ID_REQUIRED": "ID de message requis", "USER_ID_REQUIRED": "ID d'utilisateur requis", "INVALID_TICKET_ID": "ID de ticket invalide", "INVALID_MESSAGE_ID": "ID de message invalide", "INVALID_USER_ID": "ID d'utilisateur invalide", "INVALID_ORGANIZATION_ID": "ID d'organisation invalide", "CONFIG_CREATED_SUCCESSFULLY": "Configuration c<PERSON>ée avec succès", "CONFIG_UPDATED_SUCCESSFULLY": "Configuration mise à jour avec succès", "CONFIG_FETCHED_SUCCESSFULLY": "Configuration récupérée avec succès", "CONFIG_NOT_FOUND": "Configuration non trouvée", "ERROR_UPDATING_CONFIG": "Erreur lors de la mise à jour de la configuration", "ERROR_FETCHING_CONFIG": "Erreur lors de la récupération de la configuration", "PIN_VALIDATED_SUCCESSFULLY": "PIN validé avec succès", "PIN_VALIDATION_FAILED": "Échec de la validation du PIN", "ERROR_VALIDATING_PIN": "Erreur lors de la validation du PIN", "INVALID_PIN": "<PERSON><PERSON> invalide", "MESSAGE_CREATED_SUCCESSFULLY": "Message créé avec succès", "ERROR_SENDING_MESSAGE": "Erreur lors de l'envoi du message", "ERROR_FETCHING_MESSAGES": "Erreur lors de la récupération des messages", "TICKET_PRIORITY_UPDATED": "Priorité du ticket mise à jour avec succès", "TICKET_CLOSED_SUCCESSFULLY": "Ticket fermé avec succès", "TICKET_REOPENED_SUCCESSFULLY": "Ticket rouvert avec succès", "TICKET_RESOLVED_SUCCESSFULLY": "Ticket résolu avec succès", "ANALYTICS_FETCHED_SUCCESSFULLY": "Données d'analyse récupérées avec succès", "STATISTICS_FETCHED_SUCCESSFULLY": "Statistiques récupérées avec succès", "BULK_OPERATION_COMPLETED": "Opération en lot terminée avec succès", "EXPORT_COMPLETED_SUCCESSFULLY": "Exportation terminée avec succès", "IMPORT_COMPLETED_SUCCESSFULLY": "Importation terminée avec succès", "SUPPORT_PIN_REQUIRED": "Le PIN de support est requis pour créer un ticket", "INVALID_SUPPORT_PIN": "PIN de support invalide. Veuillez vérifier votre PIN et réessayer", "SUPPORT_CONFIG_NOT_FOUND": "Configuration de support non trouvée pour cette organisation", "SUPPORT_PIN_VALID": "Le PIN de support est valide", "COMMENT_ADDED_SUCCESSFULLY": "Commentaire ajouté au ticket avec succès", "COMMENT_ADD_FAILED": "Échec de l'ajout du commentaire au ticket", "COMMENTS_FETCHED_SUCCESSFULLY": "Commentaires du ticket récupérés avec succès", "COMMENTS_FETCH_FAILED": "Échec de la récupération des commentaires du ticket", "INTERNAL_COMMENT_ADDED": "Commentaire interne ajouté avec succès", "PUBLIC_COMMENT_ADDED": "Commentaire public ajouté avec succès", "TICKET_STATUS_UPDATED_SUCCESSFULLY": "Statut du ticket de support mis à jour avec succès", "TICKET_STATUS_UPDATE_FAILED": "Échec de la mise à jour du statut du ticket de support", "TICKET_MODULE_UPDATED": "Type de module du ticket de support mis à jour avec succès", "TICKET_MODULE_UPDATE_FAILED": "Échec de la mise à jour du type de module du ticket de support", "INVALID_TICKET_IDENTIFIER": "Identifiant de ticket invalide fourni", "INVALID_TICKET_STATUS": "Statut de ticket invalide fourni", "INVALID_PRIORITY": "Niveau de priorité invalide fourni", "INVALID_MODULE_TYPE": "Type de module invalide fourni", "INVALID_ISSUE_TYPE": "Type de problème invalide fourni", "INVALID_MESSAGE_TYPE": "Type de message invalide fourni", "FILE_UPLOAD_SUCCESS": "<PERSON><PERSON><PERSON> téléchargé avec succès", "FILE_SIZE_EXCEEDED": "La taille du fichier dépasse la limite maximale autorisée", "MAX_FILES_EXCEEDED": "Nombre maximum de fichiers dépassé", "REQUIRED_FIELD_MISSING": "Champ obligatoire manquant", "INVALID_EMAIL_FORMAT": "Format d'e-mail invalide fourni", "INVALID_PHONE_FORMAT": "Format de numéro de téléphone invalide", "TEXT_TOO_LONG": "Le texte dépasse la longueur maximale autorisée", "TEXT_TOO_SHORT": "Le texte est trop court. Longueur minimale requise", "ORGANIZATION_NOT_FOUND": "Organisation non trouvée", "ORGANIZATION_DISABLED": "L'organisation est désactivée", "ORGANIZATION_INACTIVE": "L'organisation est inactive", "USER_INACTIVE": "Le compte utilisateur est inactif", "USER_UNAUTHORIZED": "L'utilisateur n'est pas autorisé pour cette action", "AGENT_ASSIGNED": "Agent assigné au ticket avec succès", "AGENT_UNASSIGNED": "Agent <PERSON><PERSON><PERSON><PERSON><PERSON> du ticket avec succès", "AGENT_NOT_FOUND": "Agent non trouvé", "PRIORITY_UPDATED": "Priorité du ticket mise à jour avec succès", "STATUS_CHANGED": "Statut du ticket modifié avec succès", "MODULE_ASSIGNED": "Type de module assigné avec succès", "ATTACHMENT_ADDED": "Pièce jointe ajoutée avec succès", "ATTACHMENT_REMOVED": "Pièce jointe supprimée avec succès", "ATTACHMENT_NOT_FOUND": "Pièce jointe non trouvée", "HISTORY_RECORDED": "Action enregistrée dans l'historique du ticket", "HISTORY_FETCH_FAILED": "Échec de la récupération de l'historique du ticket", "SEARCH_RESULTS_FOUND": "Résultats de recherche trouvés", "NO_SEARCH_RESULTS": "Aucun résultat de recherche trouvé", "SEARCH_QUERY_INVALID": "Req<PERSON><PERSON>te de recherche invalide", "PAGINATION_ERROR": "Les paramètres de pagination sont invalides", "PAGE_NOT_FOUND": "Page demandée non trouvée", "FILTER_APPLIED": "Filtres appliqués avec succès", "FILTER_INVALID": "Paramètres de filtre invalides", "EMAIL_SENT": "E-mail envoy<PERSON> ave<PERSON> succès", "EMAIL_FAILED": "Échec de l'envoi de l'e-mail", "CACHE_CLEARED": "<PERSON><PERSON> vidé avec succès", "CACHE_ERROR": "Échec de l'opération de cache", "REQUEST_TIMEOUT": "<PERSON><PERSON><PERSON>'attente de la requête. Veuillez réessayer", "SUCCESS": "Opération terminée avec succès", "ERROR": "Une erreur s'est produite", "WARNING": "Attention : <PERSON><PERSON><PERSON><PERSON> réviser les informations", "INFO": "Informations mises à jour", "CREATED": "<PERSON><PERSON><PERSON> avec succès", "UPDATED": "Mis à jour avec succès", "DELETED": "Supprimé avec succès", "RETRIEVED": "Récupéré avec succès"}