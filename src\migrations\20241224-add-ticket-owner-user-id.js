'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('tickets', 'ticket_owner_user_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      comment: 'Reference to nv_users.id for registered users (enables avatar, profile data)',
      after: 'organization_id' // Add after organization_id column
    });

    // Add index for better query performance when fetching user-related data
    await queryInterface.addIndex('tickets', ['ticket_owner_user_id'], {
      name: 'idx_tickets_owner_user_id'
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove index first
    await queryInterface.removeIndex('tickets', 'idx_tickets_owner_user_id');
    
    // Remove column
    await queryInterface.removeColumn('tickets', 'ticket_owner_user_id');
  }
};
