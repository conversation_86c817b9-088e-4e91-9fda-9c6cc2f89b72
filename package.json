{"name": "backend-customer-service-ms", "version": "1.0.0", "description": "Customer Service / Support Desk microservice for TeamTrainHub with comprehensive ticket management.", "main": "index.js", "scripts": {"start": "node build/src/index.js", "start:dev": "nodemon", "dev": "ts-node --files ./src/index.ts", "build": "npx tsc -p . && xcopy /E /I src\\locales build\\src\\locales && xcopy /E /I shared build\\shared", "build:clean": "rmdir /S /Q build && npm run build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=unit", "test:integration": "jest --testPathPattern=integration", "lint": "eslint --ignore-path .eslint<PERSON>ore --ext .js,.ts .", "lint:fix": "eslint --ignore-path .eslint<PERSON>ore --ext .js,.ts . --fix", "format": "prettier --ignore-path .gitignore --write \"**/*.+(js|ts|json)\"", "format:check": "prettier --ignore-path .gitignore --check \"**/*.+(js|ts|json)\"", "type-check": "tsc --noEmit", "db:migrate": "sequelize-cli db:migrate", "db:seed": "sequelize-cli db:seed:all", "db:reset": "sequelize-cli db:drop && sequelize-cli db:create && npm run db:migrate && npm run db:seed", "docker:build": "docker build -t customer-service-ms .", "docker:run": "docker run -p 5008:5008 customer-service-ms", "prepare": "husky"}, "lint-staged": {"*": "npm run lint"}, "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.705.0", "@types/bcrypt": "^5.0.2", "@types/exceljs": "^0.5.3", "@types/handlebars": "^4.0.40", "@types/pdfkit": "^0.13.9", "amqplib": "^0.10.8", "axios": "^1.9.0", "bcrypt": "^6.0.0", "body-parser": "^1.20.3", "celebrate": "^15.0.3", "chalk": "^4.1.2", "compression": "^1.7.4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.4.7", "exceljs": "^4.4.0", "express": "^4.21.2", "express-rate-limit": "^7.4.1", "express-session": "^1.18.1", "form-data": "^4.0.3", "fs": "^0.0.1-security", "handlebars": "^4.7.8", "helmet": "^8.0.0", "http-status-codes": "^2.3.0", "i18n": "^0.15.1", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "keycloak-connect": "^26.0.7", "lodash": "^4.17.21", "mime-types": "^2.1.35", "moment": "^2.30.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "mysql2": "^3.11.5", "node-cron": "^3.0.3", "nodemailer": "^7.0.3", "path": "^0.12.7", "pdfkit": "^0.17.1", "puppeteer": "^23.11.1", "rate-limiter-flexible": "^5.0.3", "redis": "^4.6.7", "sequelize": "^6.37.5", "sequelize-cli": "^6.6.3", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^10.0.0", "yamljs": "^0.3.0"}, "devDependencies": {"@types/amqplib": "^0.10.7", "@types/chalk": "^0.4.31", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.7", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/express-session": "^1.18.0", "@types/i18n": "^0.13.12", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.7", "@types/lodash": "^4.17.13", "@types/mime-types": "^2.1.4", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.13", "@types/multer-s3": "^3.0.3", "@types/node": "^22.10.2", "@types/node-cron": "^3.0.11", "@types/supertest": "^6.0.2", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/uuid": "^10.0.0", "@types/yamljs": "^0.2.34", "@typescript-eslint/eslint-plugin": "^8.23.0", "@typescript-eslint/parser": "^8.23.0", "eslint": "^8.57.1", "husky": "^9.1.7", "jest": "^29.7.0", "lint-staged": "^15.4.3", "nodemon": "^3.1.7", "prettier": "^3.5.0", "supertest": "^7.0.0", "ts-node": "^10.9.2", "typescript": "^5.7.2"}}