import { Request, Response, NextFunction } from "express";
import { RateLimiterMemory } from "rate-limiter-flexible";
import { StatusCodes } from "http-status-codes";
import { RATE_LIMIT } from "../utils/constant";

// Create rate limiters for different endpoints
const generalRateLimiter = new RateLimiterMemory({
  points: RATE_LIMIT.MAX_REQUESTS, // Number of requests
  duration: Math.floor(RATE_LIMIT.WINDOW_MS / 1000), // Per duration in seconds
});

const aiRateLimiter = new RateLimiterMemory({
  points: RATE_LIMIT.AI_REQUESTS_PER_MINUTE, // AI requests per minute
  duration: 60, // 1 minute
});

const fileUploadRateLimiter = new RateLimiterMemory({
  points: RATE_LIMIT.FILE_UPLOAD_PER_HOUR, // File uploads per hour
  duration: 3600, // 1 hour
});

/**
 * General rate limiting middleware
 */
export const rateLimiter = async (req: Request, res: Response, next: NextFunction): Promise<any> => {
  try {
    await generalRateLimiter.consume(req.user?.id || req.ip);
    next();
  } catch (rejRes: any) {
    const remainingPoints = rejRes?.remainingPoints || 0;
    const msBeforeNext = rejRes?.msBeforeNext || 0;
    const totalHits = rejRes?.totalHits || 0;

    res.set({
      'Retry-After': Math.round(msBeforeNext / 1000) || 1,
      'X-RateLimit-Limit': RATE_LIMIT.MAX_REQUESTS,
      'X-RateLimit-Remaining': remainingPoints,
      'X-RateLimit-Reset': new Date(Date.now() + msBeforeNext).toISOString(),
    });

    return res.status(StatusCodes.TOO_MANY_REQUESTS).json({
      status: false,
      message: res.__("RATE_LIMIT_EXCEEDED") || "Too many requests",
      error: `Rate limit exceeded. Try again in ${Math.round(msBeforeNext / 1000)} seconds.`,
      details: {
        limit: RATE_LIMIT.MAX_REQUESTS,
        remaining: remainingPoints,
        resetTime: new Date(Date.now() + msBeforeNext).toISOString(),
        totalRequests: totalHits
      }
    });
  }
};

/**
 * AI-specific rate limiting middleware
 */
export const aiRateLimit = async (req: Request, res: Response, next: NextFunction): Promise<any> => {
  try {
    await aiRateLimiter.consume(req.user?.id || req.ip);
    next();
  } catch (rejRes: any) {
    const remainingPoints = rejRes?.remainingPoints || 0;
    const msBeforeNext = rejRes?.msBeforeNext || 0;

    res.set({
      'Retry-After': Math.round(msBeforeNext / 1000) || 1,
      'X-RateLimit-Limit': RATE_LIMIT.AI_REQUESTS_PER_MINUTE,
      'X-RateLimit-Remaining': remainingPoints,
      'X-RateLimit-Reset': new Date(Date.now() + msBeforeNext).toISOString(),
    });

    return res.status(StatusCodes.TOO_MANY_REQUESTS).json({
      status: false,
      message: res.__("AI_RATE_LIMIT_EXCEEDED") || "AI request limit exceeded",
      error: `Too many AI requests. Try again in ${Math.round(msBeforeNext / 1000)} seconds.`,
      details: {
        limit: RATE_LIMIT.AI_REQUESTS_PER_MINUTE,
        remaining: remainingPoints,
        resetTime: new Date(Date.now() + msBeforeNext).toISOString()
      }
    });
  }
};

/**
 * File upload rate limiting middleware
 */
export const fileUploadRateLimit = async (req: Request, res: Response, next: NextFunction): Promise<any> => {
  try {
    await fileUploadRateLimiter.consume(req.user?.id || req.ip);
    next();
  } catch (rejRes: any) {
    const remainingPoints = rejRes?.remainingPoints || 0;
    const msBeforeNext = rejRes?.msBeforeNext || 0;

    res.set({
      'Retry-After': Math.round(msBeforeNext / 1000) || 1,
      'X-RateLimit-Limit': RATE_LIMIT.FILE_UPLOAD_PER_HOUR,
      'X-RateLimit-Remaining': remainingPoints,
      'X-RateLimit-Reset': new Date(Date.now() + msBeforeNext).toISOString(),
    });

    return res.status(StatusCodes.TOO_MANY_REQUESTS).json({
      status: false,
      message: res.__("FILE_UPLOAD_RATE_LIMIT_EXCEEDED") || "File upload limit exceeded",
      error: `Too many file uploads. Try again in ${Math.round(msBeforeNext / 1000)} seconds.`,
      details: {
        limit: RATE_LIMIT.FILE_UPLOAD_PER_HOUR,
        remaining: remainingPoints,
        resetTime: new Date(Date.now() + msBeforeNext).toISOString()
      }
    });
  }
};

/**
 * Strict rate limiter for sensitive operations
 */
export const strictRateLimit = async (req: Request, res: Response, next: NextFunction): Promise<any> => {
  const strictLimiter = new RateLimiterMemory({
    points: 5, // Only 5 requests
    duration: 300, // Per 5 minutes
  });

  try {
    await strictLimiter.consume(req.user?.id || req.ip);
    next();
  } catch (rejRes: any) {
    const remainingPoints = rejRes?.remainingPoints || 0;
    const msBeforeNext = rejRes?.msBeforeNext || 0;

    res.set({
      'Retry-After': Math.round(msBeforeNext / 1000) || 1,
      'X-RateLimit-Limit': 5,
      'X-RateLimit-Remaining': remainingPoints,
      'X-RateLimit-Reset': new Date(Date.now() + msBeforeNext).toISOString(),
    });

    return res.status(StatusCodes.TOO_MANY_REQUESTS).json({
      status: false,
      message: res.__("STRICT_RATE_LIMIT_EXCEEDED") || "Rate limit exceeded for sensitive operation",
      error: `Too many requests for this operation. Try again in ${Math.round(msBeforeNext / 1000)} seconds.`,
      details: {
        limit: 5,
        remaining: remainingPoints,
        resetTime: new Date(Date.now() + msBeforeNext).toISOString()
      }
    });
  }
};

/**
 * Create custom rate limiter
 */
export const createRateLimiter = (options: {
  points: number;
  duration: number;
  keyGenerator?: (req: Request) => string;
}) => {
  const limiter = new RateLimiterMemory({
    points: options.points,
    duration: options.duration,
  });

  return async (req: Request, res: Response, next: NextFunction): Promise<any> => {
    try {
      const key = options.keyGenerator ? options.keyGenerator(req) : (req.user?.id || req.ip);
      await limiter.consume(key);
      next();
    } catch (rejRes: any) {
      const remainingPoints = rejRes?.remainingPoints || 0;
      const msBeforeNext = rejRes?.msBeforeNext || 0;

      res.set({
        'Retry-After': Math.round(msBeforeNext / 1000) || 1,
        'X-RateLimit-Limit': options.points,
        'X-RateLimit-Remaining': remainingPoints,
        'X-RateLimit-Reset': new Date(Date.now() + msBeforeNext).toISOString(),
      });

      return res.status(StatusCodes.TOO_MANY_REQUESTS).json({
        status: false,
        message: res.__("RATE_LIMIT_EXCEEDED") || "Rate limit exceeded",
        error: `Rate limit exceeded. Try again in ${Math.round(msBeforeNext / 1000)} seconds.`,
        details: {
          limit: options.points,
          remaining: remainingPoints,
          resetTime: new Date(Date.now() + msBeforeNext).toISOString()
        }
      });
    }
  };
};

export default {
  rateLimiter,
  aiRateLimit,
  fileUploadRateLimit,
  strictRateLimit,
  createRateLimiter
};
